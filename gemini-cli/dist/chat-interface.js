"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatInterface = void 0;
const readline = __importStar(require("readline"));
const chalk_1 = __importDefault(require("chalk"));
const chat_service_1 = require("./services/chat-service");
const ui_components_1 = require("./utils/ui-components");
class ChatInterface {
    constructor(configManager, rl) {
        this.isInChatMode = false;
        this.headerHeight = 0;
        this.configManager = configManager;
        this.chatService = new chat_service_1.ChatService(configManager);
        if (rl) {
            this.rl = rl;
            // Don't set up event handlers when using shared readline interface
        }
        else {
            this.rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout,
                terminal: true,
                historySize: 1000,
                removeHistoryDuplicates: true
            });
            this.setupEventHandlers();
        }
    }
    setupEventHandlers() {
        this.rl.on('line', async (input) => {
            if (this.isInChatMode) {
                await this.handleChatInput(input.trim());
            }
        });
        this.rl.on('SIGINT', () => {
            if (this.isInChatMode) {
                console.log(chalk_1.default.cyan('\n\nPress Ctrl+C again to exit chat, or type "/exit-chat" to return to main CLI.'));
                this.showChatPrompt();
            }
        });
    }
    showFixedHeader() {
        // Clear screen and move cursor to top
        process.stdout.write('\x1b[2J\x1b[H');
        // Show consistent banner only
        ui_components_1.UIComponents.showScreen();
    }
    showChatPrompt() {
        const session = this.chatService.getCurrentSession();
        const messageCount = session.messages.filter(m => m.role === 'user').length;
        process.stdout.write(chalk_1.default.blue(`💬 [${messageCount}] `));
    }
    async handleChatInput(input) {
        if (!input) {
            this.showChatPrompt();
            return;
        }
        // Handle chat commands
        if (input.startsWith('/')) {
            await this.handleChatCommand(input);
            return;
        }
        // Show user message
        console.log(chalk_1.default.green(`You: ${input}`));
        // Show typing indicator
        process.stdout.write(chalk_1.default.dim('🤖 SynthEngyne AI is thinking...'));
        try {
            const response = await this.chatService.sendMessage(input);
            // Clear typing indicator
            process.stdout.write('\r\x1b[K');
            // Show AI response
            console.log(chalk_1.default.blue(`🤖 SynthEngyne AI: ${response}`));
            console.log(); // Add spacing
        }
        catch (error) {
            // Clear typing indicator
            process.stdout.write('\r\x1b[K');
            console.log(chalk_1.default.red(`❌ Error: ${error.message}`));
            console.log(); // Add spacing
        }
        this.showChatPrompt();
    }
    async handleChatCommand(command) {
        const [cmd, ...args] = command.split(' ');
        switch (cmd.toLowerCase()) {
            case '/save-chat':
                await this.saveChatSession(args[0]);
                break;
            case '/load-chat':
                await this.loadChatSession(args[0]);
                break;
            case '/clear-chat':
                this.clearChatSession();
                break;
            case '/search-history':
                this.showSearchHistory();
                break;
            case '/clear-search':
                this.clearSearchHistory();
                break;
            case '/context':
                this.showConversationContext();
                break;
            case '/exit-chat':
                this.exitChatMode();
                return;
            case '/chat-help':
                this.showChatHelp();
                break;
            default:
                console.log(chalk_1.default.yellow(`Unknown command: ${cmd}`));
                console.log(chalk_1.default.dim('Type /chat-help for available commands'));
        }
        this.showChatPrompt();
    }
    async saveChatSession(filename) {
        try {
            const filepath = await this.chatService.saveSession(filename);
            console.log(chalk_1.default.green(`✅ Chat session saved to: ${filepath}`));
        }
        catch (error) {
            console.log(chalk_1.default.red(`❌ Failed to save session: ${error.message}`));
        }
    }
    async loadChatSession(filename) {
        if (!filename) {
            console.log(chalk_1.default.yellow('Please specify a filename: /load-chat <filename>'));
            return;
        }
        try {
            await this.chatService.loadSession(filename);
            console.log(chalk_1.default.green(`✅ Chat session loaded from: ${filename}`));
            // Show conversation history
            const session = this.chatService.getCurrentSession();
            console.log(chalk_1.default.dim(`Loaded ${session.messages.length} messages from ${session.startTime.toLocaleString()}`));
            // Show last few messages for context
            const recentMessages = session.messages.slice(-4);
            for (const msg of recentMessages) {
                const role = msg.role === 'user' ? 'You' : '🤖 SynthEngyne AI';
                const color = msg.role === 'user' ? chalk_1.default.green : chalk_1.default.blue;
                console.log(color(`${role}: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`));
            }
            console.log();
        }
        catch (error) {
            console.log(chalk_1.default.red(`❌ Failed to load session: ${error.message}`));
        }
    }
    clearChatSession() {
        this.chatService.clearCurrentSession();
        // Clear screen and show streamlined interface
        process.stdout.write('\x1b[2J\x1b[H');
        ui_components_1.UIComponents.showScreen();
        console.log(chalk_1.default.green('✅ Chat session cleared. Starting fresh conversation.'));
        console.log();
        this.showChatPrompt();
    }
    showSearchHistory() {
        const recentSearches = this.chatService.getRecentSearches(10);
        if (recentSearches.length === 0) {
            console.log(chalk_1.default.yellow('No search history available.'));
            return;
        }
        console.log(chalk_1.default.bold.blue('\n📊 Recent Web Searches:'));
        console.log(chalk_1.default.dim('─'.repeat(50)));
        recentSearches.forEach((search, index) => {
            const searchQuery = search.split('_')[0];
            console.log(chalk_1.default.cyan(`${index + 1}. ${searchQuery}`));
        });
        console.log(chalk_1.default.dim('─'.repeat(50)));
        console.log(chalk_1.default.dim('Use /clear-search to clear search history\n'));
    }
    clearSearchHistory() {
        this.chatService.clearWebSearchHistory();
        console.log(chalk_1.default.green('✅ Web search history cleared.'));
    }
    showConversationContext() {
        const context = this.chatService.getConversationContext();
        console.log(chalk_1.default.bold.blue('\n🧠 Conversation Context:'));
        console.log(chalk_1.default.dim('─'.repeat(50)));
        console.log(chalk_1.default.cyan(`Active Service: ${context.apiService || 'None'}`));
        console.log(chalk_1.default.cyan(`Messages in Session: ${context.currentSession.messages.length}`));
        console.log(chalk_1.default.cyan(`Session Started: ${context.currentSession.startTime.toLocaleString()}`));
        console.log(chalk_1.default.cyan(`Total Sessions: ${context.sessionCount}`));
        if (context.recentSearches.length > 0) {
            console.log(chalk_1.default.cyan(`Recent Searches: ${context.recentSearches.length}`));
            context.recentSearches.slice(0, 3).forEach((search) => {
                const searchQuery = search.split('_')[0];
                console.log(chalk_1.default.dim(`  • ${searchQuery}`));
            });
        }
        console.log(chalk_1.default.dim('─'.repeat(50) + '\n'));
    }
    showChatHelp() {
        const helpText = `
${chalk_1.default.bold.cyan('🤖 SynthEngyne AI Chat - Command Reference')}

${chalk_1.default.bold.blue('Session Management:')}
${chalk_1.default.cyan('  /save-chat [filename]')} - Save current conversation to file
${chalk_1.default.cyan('  /load-chat <filename>')} - Load a previously saved conversation
${chalk_1.default.cyan('  /clear-chat')}          - Clear current conversation and start fresh

${chalk_1.default.bold.blue('Search Commands:')}
${chalk_1.default.cyan('  /search-history')}      - Show recent web search history
${chalk_1.default.cyan('  /clear-search')}        - Clear web search history

${chalk_1.default.bold.blue('Utility Commands:')}
${chalk_1.default.cyan('  /context')}             - Show conversation context and statistics
${chalk_1.default.cyan('  /chat-help')}           - Show this help message
${chalk_1.default.cyan('  /exit-chat')}           - Exit chat mode and return to main CLI

${chalk_1.default.bold.blue('Chat Features:')}
• ${chalk_1.default.green('Natural conversation')} - Ask questions, get help with data generation
• ${chalk_1.default.green('Web search with memory')} - Use "search: <query>" to get current information
• ${chalk_1.default.green('Contextual search')} - Search results include context from previous searches
• ${chalk_1.default.green('Persistent memory')} - AI remembers the entire conversation context
• ${chalk_1.default.green('Code generation')} - Ask for code examples and data schemas
• ${chalk_1.default.green('MCP integration')} - Model Context Protocol for enhanced web access

${chalk_1.default.bold.blue('Example queries:')}
${chalk_1.default.dim('  "Help me design a schema for user profiles"')}
${chalk_1.default.dim('  "Search: latest trends in synthetic data generation"')}
${chalk_1.default.dim('  "Generate Python code to create sample e-commerce data"')}
${chalk_1.default.dim('  "What are current best practices for data anonymization?"')}
${chalk_1.default.dim('  "Search: new GDPR requirements for synthetic data"')}
`;
        console.log(helpText);
    }
    async startChatMode() {
        this.isInChatMode = true;
        // Show banner only once during initial transition
        this.showInitialTransition();
        // Test connection
        const connected = await this.chatService.testConnection();
        if (!connected) {
            console.log(chalk_1.default.yellow('⚠️  Warning: Could not connect to AI service. Some features may not work.'));
            console.log();
        }
        console.log(chalk_1.default.green('🎉 Welcome to SynthEngyne AI Chat! Type your message or /chat-help for commands.'));
        console.log();
        this.showChatPrompt();
    }
    showInitialTransition() {
        // Clear screen and move cursor to top
        process.stdout.write('\x1b[2J\x1b[H');
        // Show consistent banner only
        ui_components_1.UIComponents.showScreen();
    }
    exitChatMode() {
        this.isInChatMode = false;
        console.log(chalk_1.default.dim('\n👋 Exiting chat mode...'));
        // Clear screen and show the main interface
        console.clear();
        // Re-show the banner and welcome screen
        this.showMainInterface();
    }
    showMainInterface() {
        // Show consistent banner only
        ui_components_1.UIComponents.showScreen();
        // Show prompt
        this.rl.prompt();
    }
    isInChat() {
        return this.isInChatMode;
    }
    getReadlineInterface() {
        return this.rl;
    }
}
exports.ChatInterface = ChatInterface;
//# sourceMappingURL=chat-interface.js.map