"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SynthEngineREPL = void 0;
const readline = __importStar(require("readline"));
const chalk_1 = __importDefault(require("chalk"));
const config_1 = require("./config");
const chat_interface_1 = require("./chat-interface");
const ui_components_1 = require("./utils/ui-components");
class SynthEngineREPL {
    constructor() {
        this.sessionCount = 0;
        this.configManager = new config_1.ConfigManager();
        // Create readline interface with proper configuration for persistent session
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: this.getPrompt(),
            terminal: true,
            historySize: 1000,
            removeHistoryDuplicates: true
        });
        this.chatInterface = new chat_interface_1.ChatInterface(this.configManager, this.rl);
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        // Handle line input
        this.rl.on('line', async (input) => {
            await this.handleInput(input.trim());
        });
        // Handle Ctrl+C (SIGINT)
        this.rl.on('SIGINT', () => {
            console.log(chalk_1.default.cyan('\n\nPress Ctrl+C again to exit, or type "exit" to quit gracefully.'));
            this.rl.prompt();
        });
        // Handle Ctrl+D or close
        this.rl.on('close', () => {
            this.exit();
        });
        // Keep process alive - this is crucial for persistent session
        process.stdin.resume();
    }
    getPrompt() {
        const sessionInfo = chalk_1.default.dim(`[${this.sessionCount}]`);
        return `${sessionInfo} `;
    }
    async handleInput(input) {
        if (!input) {
            this.rl.prompt();
            return;
        }
        // Check if we're in chat mode and delegate to chat interface
        if (this.chatInterface.isInChat()) {
            await this.chatInterface.handleChatInput(input.trim());
            return;
        }
        // Handle exit commands (always allowed)
        if (input === 'exit' || input === 'quit' || input === '/exit' || input === '/quit') {
            this.exit();
            return;
        }
        // Check if initialization is required
        if (!this.configManager.isInitialized()) {
            // Only allow init, help, and clear commands when not initialized
            switch (input.toLowerCase()) {
                case 'help':
                case '/help':
                    this.showHelp();
                    break;
                case 'clear':
                case '/clear':
                    this.showClear();
                    return; // Don't increment counter for clear
                case 'init':
                case '/init':
                    this.showInit();
                    break;
                default:
                    if (input.toLowerCase().startsWith('/init ') || input.toLowerCase().startsWith('init ')) {
                        await this.handleInitCommand(input);
                    }
                    else {
                        console.log(chalk_1.default.yellow('⚠️  Please complete initialization first. Run ') + chalk_1.default.cyan('/init') + chalk_1.default.yellow(' to get started.'));
                    }
                    break;
            }
        }
        else {
            // Normal operation when initialized
            switch (input.toLowerCase()) {
                case 'help':
                case '/help':
                    this.showHelp();
                    break;
                case 'clear':
                case '/clear':
                    this.showClear();
                    return; // Don't increment counter for clear
                case 'chat':
                case '/chat':
                    await this.showChat();
                    return; // Don't increment counter or show prompt when entering chat mode
                case 'configure':
                case '/configure':
                    this.showConfigure();
                    break;
                case 'generate':
                case '/generate':
                    this.showGenerate();
                    break;
                case 'playground':
                case '/playground':
                    this.showPlayground();
                    break;
                case 'init':
                case '/init':
                    this.showInit();
                    break;
                default:
                    if (input.toLowerCase().startsWith('/init ') || input.toLowerCase().startsWith('init ')) {
                        await this.handleInitCommand(input);
                    }
                    else if (input.toLowerCase().startsWith('/configure ') || input.toLowerCase().startsWith('configure ')) {
                        this.handleConfigureCommand(input);
                    }
                    else {
                        // Echo unknown input
                        console.log(chalk_1.default.green('Echo:'), input);
                    }
                    break;
            }
        }
        this.sessionCount++;
        this.rl.setPrompt(this.getPrompt());
        this.rl.prompt();
    }
    showWelcome() {
        // Welcome message is now just the banner - no info box
    }
    showInitializationRequired() {
        // Initialization message is now just the banner - no info box
        console.log(chalk_1.default.yellow('⚠️  Please complete initialization first. Run ') + chalk_1.default.cyan('/init') + chalk_1.default.yellow(' to get started.'));
        console.log();
    }
    showHelp() {
        const helpText = `
${chalk_1.default.blue('Available Commands:')}
  /chat                          Interactive AI chat for data needs
  /configure                     Configure CLI settings
  /generate                      Generate synthetic data
  /playground                    Interactive data playground
  /init                          Initialize and setup configuration
  /clear                         Clear the screen and show welcome
  /exit, /quit                   Exit the CLI
`;
        console.log(helpText);
    }
    showClear() {
        console.clear();
        this.showBanner();
        // Check if initialization is required
        if (!this.configManager.isInitialized()) {
            this.showInitializationRequired();
        }
        else {
            this.showWelcome();
        }
        // Reset session counter and update prompt
        this.sessionCount = 0;
        this.rl.setPrompt(this.getPrompt());
        this.rl.prompt();
    }
    showBanner() {
        ui_components_1.UIComponents.showBanner();
    }
    async showChat() {
        console.log(chalk_1.default.green('🚀 Starting AI Chat Mode...'));
        console.log(chalk_1.default.dim('Type /exit-chat to return to the main interface\n'));
        // Start the chat interface
        await this.chatInterface.startChatMode();
    }
    showInit() {
        const initText = `
${chalk_1.default.bold.blue('Initialize SynthEngyne CLI')}

${chalk_1.default.blue('Authentication Setup:')}
  • ${chalk_1.default.cyan('DataGen Platform')} - Connect to your DataGen account
  • ${chalk_1.default.cyan('OpenRouter API')} - Use OpenRouter for AI model access
  • ${chalk_1.default.cyan('Ollama API')} - Connect to local or remote Ollama instance

${chalk_1.default.blue('Setup Commands:')}
  ${chalk_1.default.dim('/init datagen')}        Login to DataGen platform
  ${chalk_1.default.dim('/init openrouter')}     Configure OpenRouter API key
  ${chalk_1.default.dim('/init ollama')}         Setup Ollama API connection

${chalk_1.default.yellow('Note:')} You must complete at least one authentication method to use SynthEngyne CLI.
`;
        console.log(initText);
    }
    showConfigure() {
        const configText = `
${chalk_1.default.bold.blue('Configure SynthEngyne CLI')}

${chalk_1.default.blue('Configuration Options:')}
  • ${chalk_1.default.cyan('Output Directory')} - Set default output location
  • ${chalk_1.default.cyan('Default Model')} - Choose preferred AI model
  • ${chalk_1.default.cyan('Data Format')} - Set default format (JSON, CSV, Parquet)
  • ${chalk_1.default.cyan('Generation Parameters')} - Temperature, tokens, etc.

${chalk_1.default.blue('Configuration Commands:')}
  ${chalk_1.default.dim('/configure show')}      Display current configuration
  ${chalk_1.default.dim('/configure output')}    Set output directory
  ${chalk_1.default.dim('/configure model')}     Set default model
  ${chalk_1.default.dim('/configure format')}    Set default data format
  ${chalk_1.default.dim('/configure reset')}     Reset to default settings
  ${chalk_1.default.dim('/configure export')}    Export configuration file

${chalk_1.default.dim('Configuration management features coming soon...')}
`;
        console.log(configText);
    }
    showGenerate() {
        const generateText = `
${chalk_1.default.bold.blue('Generate Synthetic Data')}

${chalk_1.default.blue('Available Data Types:')}
  • Text data (articles, reviews, conversations)
  • Tabular data (customers, transactions, products)
  • Time series (metrics, sensor data, financial)
  • Images (synthetic photos, augmented datasets)

${chalk_1.default.blue('Example Usage:')}
  ${chalk_1.default.dim('generate customer-data --count 1000')}
  ${chalk_1.default.dim('generate text-reviews --category electronics')}
  ${chalk_1.default.dim('generate time-series --type financial')}

${chalk_1.default.dim('Data generation features coming soon...')}
`;
        console.log(generateText);
    }
    showPlayground() {
        const playgroundText = `
${chalk_1.default.bold.blue('Interactive Data Playground')}

${chalk_1.default.blue('Playground Features:')}
  • Live data preview and editing
  • Interactive schema design
  • Real-time generation testing
  • Data quality validation
  • Export and sharing options

${chalk_1.default.blue('Playground Modes:')}
  • Quick Start: Generate sample data instantly
  • Schema Builder: Design custom data structures
  • Template Library: Use pre-built templates
  • Validation Suite: Test data quality

${chalk_1.default.dim('Interactive playground coming soon...')}
`;
        console.log(playgroundText);
    }
    async handleInitCommand(input) {
        const parts = input.toLowerCase().split(' ');
        const command = parts[1];
        switch (command) {
            case 'datagen':
                await this.setupDataGen();
                break;
            case 'openrouter':
                await this.setupOpenRouter();
                break;
            case 'ollama':
                await this.setupOllama();
                break;
            default:
                this.showInit();
                break;
        }
    }
    handleConfigureCommand(input) {
        const parts = input.toLowerCase().split(' ');
        const command = parts[1];
        switch (command) {
            case 'show':
                const config = this.configManager.getConfig();
                console.log(chalk_1.default.blue('📋 Current Configuration:'));
                console.log(chalk_1.default.dim(JSON.stringify(config, null, 2)));
                break;
            case 'output':
                console.log(chalk_1.default.blue('📁 Output Directory Configuration'));
                console.log(chalk_1.default.yellow('Feature coming soon - Interactive directory setup'));
                break;
            case 'model':
                console.log(chalk_1.default.blue('🤖 Default Model Configuration'));
                console.log(chalk_1.default.yellow('Feature coming soon - Model selection'));
                break;
            case 'format':
                console.log(chalk_1.default.blue('📄 Data Format Configuration'));
                console.log(chalk_1.default.yellow('Feature coming soon - Format selection'));
                break;
            case 'reset':
                this.configManager.resetConfig();
                console.log(chalk_1.default.green('✅ Configuration reset to defaults'));
                break;
            case 'export':
                const exportedConfig = this.configManager.exportConfig();
                console.log(chalk_1.default.blue('📤 Exported Configuration:'));
                console.log(exportedConfig);
                break;
            default:
                this.showConfigure();
                break;
        }
    }
    async setupDataGen() {
        console.log(chalk_1.default.blue('🔐 DataGen Platform Setup'));
        console.log(chalk_1.default.dim('Please enter your DataGen platform token'));
        console.log(chalk_1.default.dim('You can find this in your DataGen account settings'));
        console.log();
        try {
            const token = await this.promptForInput('DataGen Token: ');
            if (!token || token.trim().length === 0) {
                console.log(chalk_1.default.red('❌ Token cannot be empty. Please try again.'));
                return;
            }
            // Basic validation for token format
            const trimmedToken = token.trim();
            if (trimmedToken.length < 10) {
                console.log(chalk_1.default.yellow('⚠️  Warning: Token seems too short. Please verify it\'s correct.'));
                const confirm = await this.promptForInput('Continue anyway? (y/N): ');
                if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
                    console.log(chalk_1.default.dim('Setup cancelled.'));
                    return;
                }
            }
            // Save the token
            this.configManager.setDataGenToken(trimmedToken);
            console.log(chalk_1.default.green('✅ DataGen token saved successfully!'));
            console.log(chalk_1.default.blue('🎉 Initialization complete! You can now use all SynthEngyne CLI features.'));
            console.log(chalk_1.default.dim('Run /clear to see the normal interface.'));
        }
        catch (error) {
            console.log(chalk_1.default.red('❌ Error during setup:'), error.message);
        }
    }
    async setupOpenRouter() {
        console.log(chalk_1.default.blue('🔑 OpenRouter API Setup'));
        console.log(chalk_1.default.dim('Please enter your OpenRouter API key from https://openrouter.ai/keys'));
        console.log(chalk_1.default.dim('The key should start with "sk-or-v1-"'));
        console.log();
        try {
            const apiKey = await this.promptForInput('OpenRouter API Key: ');
            if (!apiKey || apiKey.trim().length === 0) {
                console.log(chalk_1.default.red('❌ API key cannot be empty. Please try again.'));
                return;
            }
            // Basic validation for OpenRouter API key format
            const trimmedKey = apiKey.trim();
            if (!trimmedKey.startsWith('sk-or-v1-')) {
                console.log(chalk_1.default.yellow('⚠️  Warning: API key doesn\'t match expected format (should start with "sk-or-v1-")'));
                const confirm = await this.promptForInput('Continue anyway? (y/N): ');
                if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
                    console.log(chalk_1.default.dim('Setup cancelled.'));
                    return;
                }
            }
            // Save the API key
            this.configManager.setOpenRouterApiKey(trimmedKey);
            console.log(chalk_1.default.green('✅ OpenRouter API key saved successfully!'));
            console.log(chalk_1.default.blue('🎉 Initialization complete! You can now use all SynthEngyne CLI features.'));
            console.log(chalk_1.default.dim('Run /clear to see the normal interface.'));
        }
        catch (error) {
            console.log(chalk_1.default.red('❌ Error during setup:'), error.message);
        }
    }
    async setupOllama() {
        console.log(chalk_1.default.blue('🦙 Ollama API Setup'));
        console.log(chalk_1.default.dim('Configure your Ollama endpoint and optional API key'));
        console.log(chalk_1.default.dim('Default endpoint: http://localhost:11434'));
        console.log();
        try {
            // Get endpoint URL
            const endpoint = await this.promptForInput('Ollama Endpoint URL (press Enter for default): ');
            const ollamaEndpoint = endpoint.trim() || 'http://localhost:11434';
            // Validate URL format
            try {
                new URL(ollamaEndpoint);
            }
            catch {
                console.log(chalk_1.default.red('❌ Invalid URL format. Please enter a valid URL (e.g., http://localhost:11434)'));
                return;
            }
            // Get optional API key
            const apiKey = await this.promptForInput('Ollama API Key (optional, press Enter to skip): ');
            const ollamaApiKey = apiKey.trim() || 'ollama-default-key';
            // Save the configuration
            this.configManager.setOllamaConfig(ollamaApiKey, ollamaEndpoint);
            console.log(chalk_1.default.green('✅ Ollama configuration saved successfully!'));
            console.log(chalk_1.default.blue(`📡 Endpoint: ${ollamaEndpoint}`));
            if (apiKey.trim()) {
                console.log(chalk_1.default.blue('🔑 API key: [CONFIGURED]'));
            }
            else {
                console.log(chalk_1.default.blue('🔑 API key: [NOT SET]'));
            }
            console.log(chalk_1.default.blue('🎉 Initialization complete! You can now use all SynthEngyne CLI features.'));
            console.log(chalk_1.default.dim('Run /clear to see the normal interface.'));
        }
        catch (error) {
            console.log(chalk_1.default.red('❌ Error during setup:'), error.message);
        }
    }
    promptForInput(prompt) {
        return new Promise((resolve) => {
            // Use a simple approach with the existing readline interface
            process.stdout.write(prompt);
            // Set up a one-time listener
            const handleInput = (input) => {
                this.rl.removeListener('line', handleInput);
                resolve(input);
            };
            this.rl.on('line', handleInput);
        });
    }
    exit() {
        console.log(chalk_1.default.dim('\nGoodbye! 👋'));
        process.exit(0);
    }
    async start() {
        // Show banner
        this.showBanner();
        // Check if initialization is required
        if (!this.configManager.isInitialized()) {
            this.showInitializationRequired();
        }
        else {
            this.showWelcome();
        }
        // Start the interactive session
        this.rl.prompt();
        // Keep the process alive indefinitely
        return new Promise(() => {
            // This promise never resolves, keeping the process alive
            // The process will only exit when user types 'exit' or presses Ctrl+C twice
        });
    }
}
exports.SynthEngineREPL = SynthEngineREPL;
//# sourceMappingURL=repl.js.map