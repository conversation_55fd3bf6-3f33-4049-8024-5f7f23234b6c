"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataGenAPI = void 0;
const node_fetch_1 = __importDefault(require("node-fetch"));
const base_api_1 = require("./base-api");
class DataGenAPI extends base_api_1.BaseAPIService {
    constructor(config) {
        super(config, 'DataGen');
    }
    async testConnection() {
        try {
            const response = await (0, node_fetch_1.default)(`${this.config.baseUrl}/health`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: this.config.timeout
            });
            return response.ok;
        }
        catch (error) {
            console.error('DataGen connection test failed:', error);
            return false;
        }
    }
    async listModels() {
        try {
            const response = await (0, node_fetch_1.default)(`${this.config.baseUrl}/models`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: this.config.timeout
            });
            if (!response.ok) {
                throw new Error(`Failed to fetch models: ${response.status}`);
            }
            const data = await response.json();
            return data.models || [];
        }
        catch (error) {
            console.error('Failed to list DataGen models:', error);
            return ['datagen-gpt-4', 'datagen-claude', 'datagen-llama'];
        }
    }
    async sendMessage(message, session) {
        const messages = this.buildMessageHistory(session, message);
        const apiMessages = this.formatMessagesForAPI(messages);
        const requestBody = {
            model: this.config.model,
            messages: apiMessages,
            max_tokens: this.config.maxTokens,
            temperature: this.config.temperature,
            context: 'synthengyne-cli'
        };
        try {
            const response = await (0, node_fetch_1.default)(`${this.config.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'SynthEngyne-CLI/1.0.0'
                },
                body: JSON.stringify(requestBody),
                timeout: this.config.timeout
            });
            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`DataGen API error: ${response.status} ${response.statusText}\n${errorData}`);
            }
            const data = await response.json();
            if (!data.choices || data.choices.length === 0) {
                throw new Error('No response generated from DataGen API');
            }
            const content = data.choices[0].message.content;
            const usage = data.usage ? {
                promptTokens: data.usage.prompt_tokens || 0,
                completionTokens: data.usage.completion_tokens || 0,
                totalTokens: data.usage.total_tokens || 0
            } : undefined;
            return {
                content,
                usage
            };
        }
        catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error(`Unexpected error: ${error}`);
        }
    }
}
exports.DataGenAPI = DataGenAPI;
//# sourceMappingURL=datagen-api.js.map