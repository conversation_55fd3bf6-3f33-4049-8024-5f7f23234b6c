"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OllamaAPI = void 0;
const node_fetch_1 = __importDefault(require("node-fetch"));
const base_api_1 = require("./base-api");
class OllamaAPI extends base_api_1.BaseAPIService {
    constructor(config) {
        super(config, 'Ollama');
    }
    async testConnection() {
        try {
            const response = await (0, node_fetch_1.default)(`${this.config.baseUrl}/api/tags`, {
                method: 'GET',
                timeout: this.config.timeout
            });
            return response.ok;
        }
        catch (error) {
            console.error('Ollama connection test failed:', error);
            return false;
        }
    }
    async listModels() {
        try {
            const response = await (0, node_fetch_1.default)(`${this.config.baseUrl}/api/tags`, {
                method: 'GET',
                timeout: this.config.timeout
            });
            if (!response.ok) {
                throw new Error(`Failed to fetch models: ${response.status}`);
            }
            const data = await response.json();
            return data.models?.map((model) => model.name) || [];
        }
        catch (error) {
            console.error('Failed to list Ollama models:', error);
            return ['llama2', 'codellama', 'mistral'];
        }
    }
    async sendMessage(message, session) {
        const messages = this.buildMessageHistory(session, message);
        // Convert to Ollama format - combine all messages into a single prompt
        let prompt = '';
        for (const msg of messages) {
            if (msg.role === 'user') {
                prompt += `Human: ${msg.content}\n\n`;
            }
            else {
                prompt += `Assistant: ${msg.content}\n\n`;
            }
        }
        prompt += 'Assistant: ';
        const requestBody = {
            model: this.config.model,
            prompt: prompt,
            stream: false,
            options: {
                temperature: this.config.temperature,
                num_predict: this.config.maxTokens
            }
        };
        try {
            const response = await (0, node_fetch_1.default)(`${this.config.baseUrl}/api/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody),
                timeout: this.config.timeout
            });
            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`Ollama API error: ${response.status} ${response.statusText}\n${errorData}`);
            }
            const data = await response.json();
            if (!data.response) {
                throw new Error('No response generated from Ollama API');
            }
            return {
                content: data.response.trim(),
                usage: {
                    promptTokens: data.prompt_eval_count || 0,
                    completionTokens: data.eval_count || 0,
                    totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
                }
            };
        }
        catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error(`Unexpected error: ${error}`);
        }
    }
}
exports.OllamaAPI = OllamaAPI;
//# sourceMappingURL=ollama-api.js.map