import { ChatMessage, ChatSession } from '../types';
export interface APIServiceConfig {
    apiKey: string;
    baseUrl: string;
    model: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
}
export interface ChatResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
export declare abstract class BaseAPIService {
    protected config: APIServiceConfig;
    protected serviceName: string;
    constructor(config: APIServiceConfig, serviceName: string);
    abstract sendMessage(message: string, session: ChatSession): Promise<ChatResponse>;
    abstract testConnection(): Promise<boolean>;
    abstract listModels(): Promise<string[]>;
    protected buildMessageHistory(session: ChatSession, newMessage: string): ChatMessage[];
    protected formatMessagesForAPI(messages: ChatMessage[]): any[];
    getServiceName(): string;
    getConfig(): APIServiceConfig;
    updateConfig(updates: Partial<APIServiceConfig>): void;
}
//# sourceMappingURL=base-api.d.ts.map