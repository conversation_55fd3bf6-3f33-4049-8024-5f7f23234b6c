{"version": 3, "file": "web-search.js", "sourceRoot": "", "sources": ["../../src/services/web-search.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4DAA+B;AAC/B,iDAAmC;AAanC,MAAa,gBAAgB;IAK3B,YAAY,UAAkB,KAAK;QAH3B,kBAAa,GAAgC,IAAI,GAAG,EAAE,CAAC;QACvD,iBAAY,GAAwB,IAAI,GAAG,EAAE,CAAC;QAGpD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,KAAa,EAAE,UAA4B,EAAE;QACxD,MAAM,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;QAEnC,oBAAoB;QACpB,MAAM,QAAQ,GAAG,GAAG,KAAK,IAAI,UAAU,EAAE,CAAC;QAC1C,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC;YACH,oDAAoD;YACpD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YAEvE,gBAAgB;YAChB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAE1C,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAa,EAAE,UAAkB;QACtE,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,oCAAoC;QACpC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,2DAA2D;QAC3D,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC5F,OAAO,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;YACtC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,UAAkB;QAC9D,MAAM,SAAS,GAAG,iCAAiC,kBAAkB,CAAC,KAAK,CAAC,wCAAwC,CAAC;QAErH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,SAAS,EAAE;YACtC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE;gBACP,YAAY,EAAE,uBAAuB;aACtC;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;QAC1C,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,yBAAyB;QACzB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,IAAI,CAAC,OAAO,IAAI,gBAAgB;gBACvC,GAAG,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBAC3B,OAAO,EAAE,IAAI,CAAC,YAAY;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YAC5D,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7E,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACjC,OAAO,CAAC,IAAI,CAAC;wBACX,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe;wBACpD,GAAG,EAAE,KAAK,CAAC,QAAQ;wBACnB,OAAO,EAAE,KAAK,CAAC,IAAI;qBACpB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,UAAkB;QAC/D,uDAAuD;QACvD,2EAA2E;QAC3E,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,4DAA4D;QAC5D,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC1E,OAAO,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC;QAElC,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,wBAAwB,CAAC,KAAa,EAAE,UAAkB;QAChE,MAAM,OAAO,GAAmB,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEhD,sDAAsD;QACtD,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxE,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,0CAA0C;gBACjD,GAAG,EAAE,0CAA0C;gBAC/C,OAAO,EAAE,6IAA6I;aACvJ,CAAC,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxE,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,sCAAsC;gBAC7C,GAAG,EAAE,uCAAuC;gBAC5C,OAAO,EAAE,8HAA8H;aACxI,CAAC,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACtE,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,oCAAoC;gBAC3C,GAAG,EAAE,kCAAkC;gBACvC,OAAO,EAAE,gIAAgI;aAC1I,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,GAAW;QAChC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,EAAE;gBAChC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,OAAO,EAAE;oBACP,YAAY,EAAE,uBAAuB;iBACtC;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC9D,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE7B,mCAAmC;YACnC,CAAC,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,CAAC;YAE5B,sDAAsD;YACtD,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,MAAM,gBAAgB,GAAG;gBACvB,MAAM;gBACN,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,eAAe;gBACf,gBAAgB;gBAChB,MAAM;aACP,CAAC;YAEF,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;gBACxC,MAAM,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;gBAC5B,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACvB,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;oBAChC,MAAM;gBACR,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,OAAO,GAAG,OAAO;iBACd,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;iBACpB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;iBACrB,IAAI,EAAE,CAAC;YAEV,uBAAuB;YACvB,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBAC1B,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;YAC/C,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,KAAa,EAAE,UAA4B,EAAE;QACpE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAElD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,gCAAgC,KAAK,EAAE,CAAC;QACjD,CAAC;QAED,IAAI,OAAO,GAAG,uBAAuB,KAAK,QAAQ,CAAC;QAEnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;YAC7C,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACf,OAAO,IAAI,WAAW,MAAM,CAAC,GAAG,IAAI,CAAC;YACvC,CAAC;YACD,OAAO,IAAI,MAAM,MAAM,CAAC,OAAO,MAAM,CAAC;QACxC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,gDAAgD;IAChD,gBAAgB;QACd,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAED,iBAAiB,CAAC,QAAgB,CAAC;QACjC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,mBAAmB,CAAC,KAAa;QAC/B,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClD,MAAM,cAAc,GAAmB,EAAE,CAAC;QAE1C,KAAK,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvE,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YAE1E,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,cAAc,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,8BAA8B;IACpE,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,UAA4B,EAAE;QACnE,6BAA6B;QAC7B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEzD,qCAAqC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAEvD,IAAI,iBAAiB,GAAG,+BAA+B,KAAK,QAAQ,CAAC;QAErE,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACjC,iBAAiB,IAAI,GAAG,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC;YACvD,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACf,iBAAiB,IAAI,WAAW,MAAM,CAAC,GAAG,IAAI,CAAC;YACjD,CAAC;YACD,iBAAiB,IAAI,MAAM,MAAM,CAAC,OAAO,MAAM,CAAC;QAClD,CAAC;QAED,mCAAmC;QACnC,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,iBAAiB,IAAI,mDAAmD,CAAC;YAEzE,MAAM,aAAa,GAAG,cAAc;iBACjC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAC9B,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CACxD;iBACA,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,MAAM,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBAChC,iBAAiB,IAAI,OAAO,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC;YACpE,CAAC;QACH,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,YAAY;QACV,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAED,mBAAmB;QACjB,OAAO;YACL,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAClD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,IAAS;QAC3B,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;CACF;AA5SD,4CA4SC"}