import { BaseAPIService, APIServiceConfig, ChatResponse } from './base-api';
import { ChatSession } from '../types';
export declare class DataGenAPI extends BaseAPIService {
    constructor(config: APIServiceConfig);
    testConnection(): Promise<boolean>;
    listModels(): Promise<string[]>;
    sendMessage(message: string, session: ChatSession): Promise<ChatResponse>;
}
//# sourceMappingURL=datagen-api.d.ts.map