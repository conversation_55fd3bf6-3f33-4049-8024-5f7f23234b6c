export interface SearchResult {
    title: string;
    url: string;
    snippet: string;
}
export interface WebSearchOptions {
    maxResults?: number;
    timeout?: number;
}
export declare class WebSearchService {
    private readonly timeout;
    private searchHistory;
    private contentCache;
    constructor(timeout?: number);
    search(query: string, options?: WebSearchOptions): Promise<SearchResult[]>;
    private performMultiSourceSearch;
    private searchDuckDuckGo;
    private searchAlternative;
    private generateSyntheticResults;
    fetchPageContent(url: string): Promise<string>;
    searchAndSummarize(query: string, options?: WebSearchOptions): Promise<string>;
    getSearchHistory(): Map<string, SearchResult[]>;
    getRecentSearches(limit?: number): string[];
    findRelatedSearches(query: string): SearchResult[];
    searchWithContext(query: string, options?: WebSearchOptions): Promise<string>;
    clearHistory(): void;
    exportSearchHistory(): any;
    importSearchHistory(data: any): void;
}
//# sourceMappingURL=web-search.d.ts.map