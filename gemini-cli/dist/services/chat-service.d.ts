import { ConfigManager } from '../config';
import { ChatSession } from '../types';
export declare class ChatService {
    private configManager;
    private currentSession;
    private apiService;
    private webSearch;
    private sessionHistory;
    constructor(configManager: ConfigManager);
    private createNewSession;
    private initializeAPIService;
    sendMessage(message: string): Promise<string>;
    getCurrentSession(): ChatSession;
    clearCurrentSession(): void;
    getAPIServiceInfo(): string;
    testConnection(): Promise<boolean>;
    saveSession(filename?: string): Promise<string>;
    loadSession(filename: string): Promise<void>;
    getSessionHistory(): ChatSession[];
    getWebSearchHistory(): Map<string, any[]>;
    getRecentSearches(limit?: number): string[];
    clearWebSearchHistory(): void;
    performContextualSearch(query: string): Promise<string>;
    getConversationContext(): any;
    enhanceMessageWithContext(message: string): Promise<string>;
    private extractSearchQuery;
}
//# sourceMappingURL=chat-service.d.ts.map