import { BaseAPIService, APIServiceConfig, ChatResponse } from './base-api';
import { ChatSession } from '../types';
export declare class OpenRouterAPI extends BaseAPIService {
    constructor(config: APIServiceConfig);
    testConnection(): Promise<boolean>;
    listModels(): Promise<string[]>;
    sendMessage(message: string, session: ChatSession): Promise<ChatResponse>;
}
//# sourceMappingURL=openrouter-api.d.ts.map