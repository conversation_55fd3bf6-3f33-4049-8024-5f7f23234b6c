"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAPIService = void 0;
class BaseAPIService {
    constructor(config, serviceName) {
        this.config = config;
        this.serviceName = serviceName;
    }
    buildMessageHistory(session, newMessage) {
        const messages = [];
        // Add system message if this is the first message
        if (session.messages.length === 0) {
            messages.push({
                role: 'assistant',
                content: `You are SynthEngyne AI, an intelligent assistant specialized in synthetic data generation and data science. You help users with:

- Synthetic data generation strategies and best practices
- Data modeling and schema design
- Data analysis and insights
- Technical questions about data formats and structures
- Code generation for data processing
- Web research for current information when needed

You have access to web search capabilities to provide up-to-date information. Be helpful, accurate, and technical when appropriate.`,
                timestamp: new Date()
            });
        }
        // Add recent conversation history (last 20 messages to manage context)
        const recentMessages = session.messages.slice(-20);
        messages.push(...recentMessages);
        // Add the new message
        messages.push({
            role: 'user',
            content: newMessage,
            timestamp: new Date()
        });
        return messages;
    }
    formatMessagesForAPI(messages) {
        return messages.map(msg => ({
            role: msg.role === 'assistant' ? 'assistant' : 'user',
            content: msg.content
        }));
    }
    getServiceName() {
        return this.serviceName;
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
    }
}
exports.BaseAPIService = BaseAPIService;
//# sourceMappingURL=base-api.js.map