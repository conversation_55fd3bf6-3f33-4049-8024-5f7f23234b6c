import * as readline from 'readline';
import { ConfigManager } from './config';
export declare class ChatInterface {
    private rl;
    private configManager;
    private chatService;
    private isInChatMode;
    private headerHeight;
    constructor(configManager: ConfigManager, rl?: readline.Interface);
    private setupEventHandlers;
    private showFixedHeader;
    private showChatPrompt;
    handleChatInput(input: string): Promise<void>;
    private handleChatCommand;
    private saveChatSession;
    private loadChatSession;
    private clearChatSession;
    private showSearchHistory;
    private clearSearchHistory;
    private showConversationContext;
    private showChatHelp;
    startChatMode(): Promise<void>;
    private showInitialTransition;
    private exitChatMode;
    private showMainInterface;
    isInChat(): boolean;
    getReadlineInterface(): readline.Interface;
}
//# sourceMappingURL=chat-interface.d.ts.map