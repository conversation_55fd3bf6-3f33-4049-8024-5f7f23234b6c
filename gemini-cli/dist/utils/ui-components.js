"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIComponents = void 0;
const chalk_1 = __importDefault(require("chalk"));
/**
 * Centralized UI components for consistent banner display
 * across all screens in the Synthengyne CLI application
 */
class UIComponents {
    /**
     * Display the consistent SYNTHENGYNE banner using ASCII art
     * This should be used across all screens for consistency
     */
    static showBanner() {
        const banner = `
 ███████╗██╗   ██╗███╗   ██╗████████╗██╗  ██╗███████╗███╗   ██╗ ██████╗██╗   ██╗███╗   ██╗███████╗
 ██╔════╝╚██╗ ██╔╝████╗  ██║╚══██╔══╝██║  ██║██╔════╝████╗  ██║██╔════╝╚██╗ ██╔╝████╗  ██║██╔════╝
 ███████╗ ╚████╔╝ ██╔██╗ ██║   ██║   ███████║█████╗  ██╔██╗ ██║██║  ███╗╚████╔╝ ██╔██╗ ██║█████╗
 ╚════██║  ╚██╔╝  ██║╚██╗██║   ██║   ██╔══██║██╔══╝  ██║╚██╗██║██║   ██║ ╚██╔╝  ██║╚██╗██║██╔══╝
 ███████║   ██║   ██║ ╚████║   ██║   ██║  ██║███████╗██║ ╚████║╚██████╔╝  ██║   ██║ ╚████║███████╗
 ╚══════╝   ╚═╝   ╚═╝  ╚═══╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝   ╚═╝   ╚═╝  ╚═══╝╚══════╝`;
        console.log(chalk_1.default.blue(banner));
        console.log(); // Add spacing after banner
    }
    /**
     * Clear screen and show banner only
     * This is the main method to use for consistent screen transitions
     */
    static showScreen() {
        // Clear screen
        console.clear();
        // Show consistent banner
        this.showBanner();
    }
    /**
     * Show just the banner without clearing screen
     * Useful for refreshing the display without losing scroll history
     */
    static refreshDisplay() {
        this.showBanner();
    }
}
exports.UIComponents = UIComponents;
//# sourceMappingURL=ui-components.js.map