"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIComponents = void 0;
const chalk_1 = __importDefault(require("chalk"));
/**
 * Centralized UI components for consistent banner and contextual help display
 * across all screens in the Synthengyne CLI application
 */
class UIComponents {
    /**
     * Get the terminal width for centering calculations
     */
    static getTerminalWidth() {
        return process.stdout.columns || 80;
    }
    /**
     * Center text horizontally on the screen
     */
    static centerText(text) {
        const terminalWidth = this.getTerminalWidth();
        const textLines = text.split('\n');
        const maxLineLength = Math.max(...textLines.map(line => line.length));
        const leftPadding = Math.max(0, Math.floor((terminalWidth - maxLineLength) / 2));
        return textLines
            .map(line => ' '.repeat(leftPadding) + line)
            .join('\n');
    }
    /**
     * Display the consistent SYNTHENGYNE banner using ASCII art, centered horizontally
     * This should be used across all screens for consistency
     */
    static showBanner() {
        const banner = `
 ███████╗██╗   ██╗███╗   ██╗████████╗██╗  ██╗███████╗███╗   ██╗ ██████╗██╗   ██╗███╗   ██╗███████╗
 ██╔════╝╚██╗ ██╔╝████╗  ██║╚══██╔══╝██║  ██║██╔════╝████╗  ██║██╔════╝╚██╗ ██╔╝████╗  ██║██╔════╝
 ███████╗ ╚████╔╝ ██╔██╗ ██║   ██║   ███████║█████╗  ██╔██╗ ██║██║  ███╗╚████╔╝ ██╔██╗ ██║█████╗
 ╚════██║  ╚██╔╝  ██║╚██╗██║   ██║   ██╔══██║██╔══╝  ██║╚██╗██║██║   ██║ ╚██╔╝  ██║╚██╗██║██╔══╝
 ███████║   ██║   ██║ ╚████║   ██║   ██║  ██║███████╗██║ ╚████║╚██████╔╝  ██║   ██║ ╚████║███████╗
 ╚══════╝   ╚═╝   ╚═╝  ╚═══╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝   ╚═╝   ╚═╝  ╚═══╝╚══════╝`;
        const centeredBanner = this.centerText(banner);
        console.log(chalk_1.default.blue(centeredBanner));
        console.log(); // Add spacing after banner
    }
    /**
     * Display a contextual help line, centered horizontally
     */
    static showContextualHelp(helpText) {
        const centeredHelp = this.centerText(helpText);
        console.log(chalk_1.default.dim(centeredHelp));
        console.log(); // Add spacing after help line
    }
    /**
     * Get contextual help text for different application modes
     */
    static getContextualHelpText(mode) {
        switch (mode) {
            case 'main':
                return 'Type /help for available commands';
            case 'chat':
                return 'Type /chat-help for chat commands';
            case 'config':
                return 'Type /config help for configuration options';
            case 'generate':
                return 'Type /generate help for data generation options';
            case 'playground':
                return 'Type /playground help for playground commands';
            case 'init':
                return 'Type /init for initialization options';
            default:
                return 'Type /help for available commands';
        }
    }
    /**
     * Clear screen and show banner with contextual help
     * This is the main method to use for consistent screen transitions
     */
    static showScreen(mode = 'main') {
        // Clear screen
        console.clear();
        // Show consistent centered banner
        this.showBanner();
        // Show contextual help
        const helpText = this.getContextualHelpText(mode);
        this.showContextualHelp(helpText);
    }
    /**
     * Show just the banner and contextual help without clearing screen
     * Useful for refreshing the display without losing scroll history
     */
    static refreshDisplay(mode = 'main') {
        this.showBanner();
        // Show contextual help
        const helpText = this.getContextualHelpText(mode);
        this.showContextualHelp(helpText);
    }
}
exports.UIComponents = UIComponents;
//# sourceMappingURL=ui-components.js.map