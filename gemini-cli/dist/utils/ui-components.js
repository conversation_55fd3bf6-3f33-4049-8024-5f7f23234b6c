"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIComponents = void 0;
const chalk_1 = __importDefault(require("chalk"));
/**
 * Centralized UI components for consistent banner and info box display
 * across all screens in the Synthengyne CLI application
 */
class UIComponents {
    /**
     * Display the consistent SYNTHENGYNE banner using ASCII art
     * This should be used across all screens for consistency
     */
    static showBanner() {
        const banner = `
 ███████╗██╗   ██╗███╗   ██╗████████╗██╗  ██╗███████╗███╗   ██╗ ██████╗██╗   ██╗███╗   ██╗███████╗
 ██╔════╝╚██╗ ██╔╝████╗  ██║╚══██╔══╝██║  ██║██╔════╝████╗  ██║██╔════╝╚██╗ ██╔╝████╗  ██║██╔════╝
 ███████╗ ╚████╔╝ ██╔██╗ ██║   ██║   ███████║█████╗  ██╔██╗ ██║██║  ███╗╚████╔╝ ██╔██╗ ██║█████╗
 ╚════██║  ╚██╔╝  ██║╚██╗██║   ██║   ██╔══██║██╔══╝  ██║╚██╗██║██║   ██║ ╚██╔╝  ██║╚██╗██║██╔══╝
 ███████║   ██║   ██║ ╚████║   ██║   ██║  ██║███████╗██║ ╚████║╚██████╔╝  ██║   ██║ ╚████║███████╗
 ╚══════╝   ╚═╝   ╚═╝  ╚═══╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝   ╚═╝   ╚═╝  ╚═══╝╚══════╝`;
        console.log(chalk_1.default.blue(banner));
        console.log(); // Add spacing after banner
    }
    /**
     * Display the main welcome info box
     * Used on the main screen when the application starts
     */
    static showMainInfoBox() {
        const infoBox = `
          ╭──────────────────────────────────────────────────────────────────────────────╮
          │                                                                              │
          │                          Welcome to SynthEngyne CLI                          │
          │                                                                              │
          │            Powerful synthetic data generation platform by DataGen            │
          │           Create high-quality datasets across multiple modalities            │
          │                                                                              │
          │  Commands: /chat | /configure | /generate | /playground | /init | /clear |   │
          │                                    /exit                                     │
          │                                                                              │
          ╰──────────────────────────────────────────────────────────────────────────────╯`;
        console.log(chalk_1.default.cyan(infoBox));
        console.log(); // Add spacing after info box
    }
    /**
     * Display the initialization required info box
     * Used when the application needs to be initialized
     */
    static showInitializationInfoBox() {
        const infoBox = `
          ╭──────────────────────────────────────────────────────────────────────────────╮
          │                                                                              │
          │                        ⚠️  Initialization Required                           │
          │                                                                              │
          │         Welcome to SynthEngyne CLI! To get started, you need to             │
          │                    set up authentication for data generation.               │
          │                                                                              │
          │  Setup Options:                                                              │
          │  • /init datagen    - Login to DataGen platform                             │
          │  • /init openrouter - Configure OpenRouter API                              │
          │  • /init ollama     - Setup Ollama API                                      │
          │                                                                              │
          │  Run /init to see all setup options                                         │
          │                                                                              │
          ╰──────────────────────────────────────────────────────────────────────────────╯`;
        console.log(chalk_1.default.cyan(infoBox));
        console.log(); // Add spacing after info box
    }
    /**
     * Display the chat mode info box
     * Used when entering chat mode
     */
    static showChatInfoBox() {
        const infoBox = `
╭─────────────────────────────────────────────────────────────────────────────────────╮
│ ● AI Chat Mode (Enhanced)                                                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ Active Services: OpenRouter: ${chalk_1.default.green('Enabled'.padEnd(15))} │ Sessions: /save-chat /load-chat /list-chats      │
│  Web Search: ${chalk_1.default.green('Enabled'.padEnd(15))} │ Context: /context /chat-help /exit-chat         │
│  MCP Protocol: ${chalk_1.default.green('Enabled'.padEnd(15))} │ Utility: /context /chat-help /exit-chat           │
╰─────────────────────────────────────────────────────────────────────────────────────╯`;
        console.log(chalk_1.default.cyan(infoBox));
        console.log(); // Add spacing after info box
    }
    /**
     * Display the configuration info box
     * Used when in configuration mode
     */
    static showConfigInfoBox() {
        const infoBox = `
          ╭──────────────────────────────────────────────────────────────────────────────╮
          │                                                                              │
          │                           Configuration Mode                                 │
          │                                                                              │
          │                    Configure your SynthEngyne CLI settings                  │
          │                                                                              │
          │  Commands: /config show | /config set <key> <value> | /config reset |       │
          │           /config api <provider> | /config export | /config import |        │
          │                                    /back                                     │
          │                                                                              │
          ╰──────────────────────────────────────────────────────────────────────────────╯`;
        console.log(chalk_1.default.cyan(infoBox));
        console.log(); // Add spacing after info box
    }
    /**
     * Display the data generation info box
     * Used when in data generation mode
     */
    static showGenerateInfoBox() {
        const infoBox = `
          ╭──────────────────────────────────────────────────────────────────────────────╮
          │                                                                              │
          │                          Data Generation Mode                                │
          │                                                                              │
          │                   Generate synthetic data across modalities                 │
          │                                                                              │
          │  Commands: /generate <type> | /templates | /validate <file> |               │
          │           /export <format> | /stats | /preview | /back                      │
          │                                                                              │
          ╰──────────────────────────────────────────────────────────────────────────────╯`;
        console.log(chalk_1.default.cyan(infoBox));
        console.log(); // Add spacing after info box
    }
    /**
     * Display the playground info box
     * Used when in playground mode
     */
    static showPlaygroundInfoBox() {
        const infoBox = `
          ╭──────────────────────────────────────────────────────────────────────────────╮
          │                                                                              │
          │                            Playground Mode                                   │
          │                                                                              │
          │                 Interactive environment for data experimentation            │
          │                                                                              │
          │  Commands: /experiment <type> | /load <dataset> | /analyze |                │
          │           /visualize | /export | /save | /back                              │
          │                                                                              │
          ╰──────────────────────────────────────────────────────────────────────────────╯`;
        console.log(chalk_1.default.cyan(infoBox));
        console.log(); // Add spacing after info box
    }
    /**
     * Clear screen and show banner with specified info box
     * This is the main method to use for consistent screen transitions
     */
    static showScreen(infoBoxType) {
        // Clear screen
        console.clear();
        // Show consistent banner
        this.showBanner();
        // Show appropriate info box
        switch (infoBoxType) {
            case 'main':
                this.showMainInfoBox();
                break;
            case 'init':
                this.showInitializationInfoBox();
                break;
            case 'chat':
                this.showChatInfoBox();
                break;
            case 'config':
                this.showConfigInfoBox();
                break;
            case 'generate':
                this.showGenerateInfoBox();
                break;
            case 'playground':
                this.showPlaygroundInfoBox();
                break;
        }
    }
    /**
     * Show just the banner and info box without clearing screen
     * Useful for refreshing the display without losing scroll history
     */
    static refreshDisplay(infoBoxType) {
        this.showBanner();
        switch (infoBoxType) {
            case 'main':
                this.showMainInfoBox();
                break;
            case 'init':
                this.showInitializationInfoBox();
                break;
            case 'chat':
                this.showChatInfoBox();
                break;
            case 'config':
                this.showConfigInfoBox();
                break;
            case 'generate':
                this.showGenerateInfoBox();
                break;
            case 'playground':
                this.showPlaygroundInfoBox();
                break;
        }
    }
}
exports.UIComponents = UIComponents;
//# sourceMappingURL=ui-components.js.map