/**
 * Environment Configuration Utility
 * Centralizes all environment variable handling with type safety and defaults
 */
export interface EnvironmentConfig {
    openrouterApiKey: string;
    ollamaApiKey: string;
    datagenToken: string;
    ollamaUrl: string;
    openrouterBaseUrl: string;
    datagenBaseUrl: string;
    defaultModel: string;
    outputDirectory: string;
    dataFormat: 'json' | 'csv' | 'parquet';
    maxTokens: number;
    temperature: number;
    topP: number;
    apiTimeout: number;
    maxSamples: number;
    defaultBatchSize: number;
    compressionLevel: number;
    enableValidation: boolean;
    autoSave: boolean;
    verbose: boolean;
    theme: 'dark' | 'light' | 'auto';
}
/**
 * Load and validate environment configuration
 */
export declare function loadEnvironmentConfig(): EnvironmentConfig;
/**
 * Validate required environment variables
 */
export declare function validateEnvironmentConfig(config: EnvironmentConfig): string[];
/**
 * Get environment configuration with validation
 */
export declare function getEnvironmentConfig(): EnvironmentConfig;
//# sourceMappingURL=env-config.d.ts.map