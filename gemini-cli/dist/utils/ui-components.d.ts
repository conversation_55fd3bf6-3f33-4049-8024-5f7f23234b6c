/**
 * Centralized UI components for consistent banner and info box display
 * across all screens in the Synthengyne CLI application
 */
export declare class UIComponents {
    /**
     * Display the consistent SYNTHENGYNE banner using ASCII art
     * This should be used across all screens for consistency
     */
    static showBanner(): void;
    /**
     * Display the main welcome info box
     * Used on the main screen when the application starts
     */
    static showMainInfoBox(): void;
    /**
     * Display the initialization required info box
     * Used when the application needs to be initialized
     */
    static showInitializationInfoBox(): void;
    /**
     * Display the chat mode info box
     * Used when entering chat mode
     */
    static showChatInfoBox(): void;
    /**
     * Display the configuration info box
     * Used when in configuration mode
     */
    static showConfigInfoBox(): void;
    /**
     * Display the data generation info box
     * Used when in data generation mode
     */
    static showGenerateInfoBox(): void;
    /**
     * Display the playground info box
     * Used when in playground mode
     */
    static showPlaygroundInfoBox(): void;
    /**
     * Clear screen and show banner with specified info box
     * This is the main method to use for consistent screen transitions
     */
    static showScreen(infoBoxType: 'main' | 'init' | 'chat' | 'config' | 'generate' | 'playground'): void;
    /**
     * Show just the banner and info box without clearing screen
     * Useful for refreshing the display without losing scroll history
     */
    static refreshDisplay(infoBoxType: 'main' | 'init' | 'chat' | 'config' | 'generate' | 'playground'): void;
}
//# sourceMappingURL=ui-components.d.ts.map