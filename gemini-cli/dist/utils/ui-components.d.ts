/**
 * Centralized UI components for consistent banner display
 * across all screens in the Synthengyne CLI application
 */
export declare class UIComponents {
    /**
     * Display the consistent SYNTHENGYNE banner using ASCII art
     * This should be used across all screens for consistency
     */
    static showBanner(): void;
    /**
     * Clear screen and show banner only
     * This is the main method to use for consistent screen transitions
     */
    static showScreen(): void;
    /**
     * Show just the banner without clearing screen
     * Useful for refreshing the display without losing scroll history
     */
    static refreshDisplay(): void;
}
//# sourceMappingURL=ui-components.d.ts.map