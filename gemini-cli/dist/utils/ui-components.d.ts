/**
 * Centralized UI components for consistent banner and contextual help display
 * across all screens in the Synthengyne CLI application
 */
export declare class UIComponents {
    /**
     * Get the terminal width for centering calculations
     */
    private static getTerminalWidth;
    /**
     * Center text horizontally on the screen
     */
    private static centerText;
    /**
     * Display the consistent SYNTHENGYNE banner using ASCII art, centered horizontally
     * This should be used across all screens for consistency
     */
    static showBanner(): void;
    /**
     * Display a contextual help line, centered horizontally
     */
    static showContextualHelp(helpText: string): void;
    /**
     * Get contextual help text for different application modes
     */
    private static getContextualHelpText;
    /**
     * Clear screen and show banner with contextual help
     * This is the main method to use for consistent screen transitions
     */
    static showScreen(mode?: 'main' | 'chat' | 'config' | 'generate' | 'playground' | 'init'): void;
    /**
     * Show just the banner and contextual help without clearing screen
     * Useful for refreshing the display without losing scroll history
     */
    static refreshDisplay(mode?: 'main' | 'chat' | 'config' | 'generate' | 'playground' | 'init'): void;
}
//# sourceMappingURL=ui-components.d.ts.map