export declare class SynthEngineREPL {
    private rl;
    private sessionCount;
    private configManager;
    private chatInterface;
    constructor();
    private setupEventHandlers;
    private getPrompt;
    private handleInput;
    private showWelcome;
    private showInitializationRequired;
    private showHelp;
    private showClear;
    private showBanner;
    private showChat;
    private showInit;
    private showConfigure;
    private showGenerate;
    private showPlayground;
    private handleInitCommand;
    private handleConfigureCommand;
    private setupDataGen;
    private setupOpenRouter;
    private setupOllama;
    private promptForInput;
    private exit;
    start(): Promise<void>;
}
//# sourceMappingURL=repl.d.ts.map