{"version": 3, "file": "chat-interface.js", "sourceRoot": "", "sources": ["../src/chat-interface.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mDAAqC;AACrC,kDAA0B;AAE1B,0DAAsD;AACtD,yDAAqD;AAErD,MAAa,aAAa;IAOxB,YAAY,aAA4B,EAAE,EAAuB;QAHzD,iBAAY,GAAY,KAAK,CAAC;QAC9B,iBAAY,GAAW,CAAC,CAAC;QAG/B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,0BAAW,CAAC,aAAa,CAAC,CAAC;QAElD,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACb,mEAAmE;QACrE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;gBACjC,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,IAAI;gBACjB,uBAAuB,EAAE,IAAI;aAC9B,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAa,EAAE,EAAE;YACzC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACxB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC,CAAC;gBAC5G,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,eAAe;QACrB,sCAAsC;QACtC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAEtC,wCAAwC;QACxC,4BAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAIO,cAAc;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;QACrD,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QAC5E,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,YAAY,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,KAAa;QACxC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,uBAAuB;QACvB,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,oBAAoB;QACpB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC;QAE1C,wBAAwB;QACxB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC,CAAC;QAEpE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE3D,yBAAyB;YACzB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEjC,mBAAmB;YACnB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC1D,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,cAAc;QAE/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,yBAAyB;YACzB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEjC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,YAAa,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAC/D,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,cAAc;QAC/B,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAe;QAC7C,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE1C,QAAQ,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;YAC1B,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,aAAa;gBAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM;YACR,KAAK,iBAAiB;gBACpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,MAAM;YACR,KAAK,eAAe;gBAClB,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;YACR,KAAK,UAAU;gBACb,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,MAAM;YACR,KAAK,YAAY;gBACf,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,OAAO;YACT,KAAK,YAAY;gBACf,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,MAAM;YACR;gBACE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC,CAAC;gBACrD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAiB;QAC7C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC9D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,6BAA8B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAiB;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,kDAAkD,CAAC,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC7C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC,CAAC;YAEpE,4BAA4B;YAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,kBAAkB,OAAO,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;YAEhH,qCAAqC;YACrC,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;gBACjC,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB,CAAC;gBAC/D,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC;gBAC7D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1G,CAAC;YACD,OAAO,CAAC,GAAG,EAAE,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,6BAA8B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;QAEvC,8CAA8C;QAC9C,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACtC,4BAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAEhC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC,CAAC;QACjF,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,iBAAiB;QACvB,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QAE9D,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEvC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,EAAE,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC,CAAC;IACxE,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,uBAAuB;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,CAAC;QAE1D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,mBAAmB,OAAO,CAAC,UAAU,IAAI,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC1F,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,oBAAoB,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;QACjG,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,mBAAmB,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAEnE,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,oBAAoB,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YAC7E,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAc,EAAE,EAAE;gBAC5D,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,OAAO,WAAW,EAAE,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IAChD,CAAC;IAEO,YAAY;QAClB,MAAM,QAAQ,GAAG;EACnB,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,4CAA4C,CAAC;;EAE7D,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;EACtC,eAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC;EACrC,eAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC;EACrC,eAAK,CAAC,IAAI,CAAC,eAAe,CAAC;;EAE3B,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;EACnC,eAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC;EAC/B,eAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC;;EAE7B,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC;EACpC,eAAK,CAAC,IAAI,CAAC,YAAY,CAAC;EACxB,eAAK,CAAC,IAAI,CAAC,cAAc,CAAC;EAC1B,eAAK,CAAC,IAAI,CAAC,cAAc,CAAC;;EAE1B,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;IAC/B,eAAK,CAAC,KAAK,CAAC,sBAAsB,CAAC;IACnC,eAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC;IACrC,eAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC;IAChC,eAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC;IAChC,eAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC;IAC9B,eAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC;;EAEhC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC;EACnC,eAAK,CAAC,GAAG,CAAC,+CAA+C,CAAC;EAC1D,eAAK,CAAC,GAAG,CAAC,wDAAwD,CAAC;EACnE,eAAK,CAAC,GAAG,CAAC,2DAA2D,CAAC;EACtE,eAAK,CAAC,GAAG,CAAC,6DAA6D,CAAC;EACxE,eAAK,CAAC,GAAG,CAAC,sDAAsD,CAAC;CAClE,CAAC;QACE,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxB,CAAC;IAEM,KAAK,CAAC,aAAa;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,kDAAkD;QAClD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,kBAAkB;QAClB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;QAC1D,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,2EAA2E,CAAC,CAAC,CAAC;YACvG,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,kFAAkF,CAAC,CAAC,CAAC;QAC7G,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAEO,qBAAqB;QAC3B,sCAAsC;QACtC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAEtC,wCAAwC;QACxC,4BAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC,CAAC;QAEpD,2CAA2C;QAC3C,OAAO,CAAC,KAAK,EAAE,CAAC;QAEhB,wCAAwC;QACxC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,iBAAiB;QACvB,wCAAwC;QACxC,4BAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAEhC,cAAc;QACd,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;IACnB,CAAC;IAEM,QAAQ;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEM,oBAAoB;QACzB,OAAO,IAAI,CAAC,EAAE,CAAC;IACjB,CAAC;CACF;AA5TD,sCA4TC"}