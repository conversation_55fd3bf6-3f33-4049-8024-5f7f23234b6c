{"name": "synthengyne-cli", "version": "1.0.0", "description": "SynthEngyne CLI - A synthetic data generation tool by Datagen for creating high-quality synthetic datasets across multiple modalities", "main": "dist/index.js", "bin": {"synthengyne": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "test:env": "node -e \"require('dotenv').config(); console.log('Environment variables loaded:', Object.keys(process.env).filter(k => k.startsWith('DATAGEN_') || k.startsWith('OPENROUTER_') || k.startsWith('OLLAMA_')).length > 0 ? '✅ API keys configured' : '❌ No API keys found');\""}, "keywords": ["cli", "synthetic-data", "data-generation", "datagen", "synthengyne", "machine-learning", "datasets", "terminal"], "author": "", "license": "Apache-2.0", "engines": {"node": ">=18.0.0"}, "dependencies": {"@types/node-fetch": "^2.6.13", "axios": "^1.11.0", "boxen": "^8.0.1", "chalk": "^5.5.0", "cheerio": "^1.1.2", "dotenv": "^17.2.1", "figlet": "^1.8.2", "gradient-string": "^3.0.0", "node-fetch": "^2.7.0", "readline": "^1.3.0"}, "devDependencies": {"@types/figlet": "^1.7.0", "@types/node": "^24.2.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}