import * as readline from 'readline';
import chalk from 'chalk';
import { ConfigManager } from './config';
import { ChatInterface } from './chat-interface';
import { UIComponents } from './utils/ui-components';

export class SynthEngineREPL {
  private rl: readline.Interface;
  private sessionCount: number = 0;
  private configManager: ConfigManager;
  private chatInterface: ChatInterface;

  constructor() {
    this.configManager = new ConfigManager();

    // Create readline interface with proper configuration for persistent session
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: this.getPrompt(),
      terminal: true,
      historySize: 1000,
      removeHistoryDuplicates: true
    });

    this.chatInterface = new ChatInterface(this.configManager, this.rl);
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Handle line input
    this.rl.on('line', async (input: string) => {
      await this.handleInput(input.trim());
    });

    // Handle Ctrl+C (SIGINT)
    this.rl.on('SIGINT', () => {
      console.log(chalk.cyan('\n\nPress Ctrl+C again to exit, or type "exit" to quit gracefully.'));
      this.rl.prompt();
    });

    // Handle Ctrl+D or close
    this.rl.on('close', () => {
      this.exit();
    });

    // Keep process alive - this is crucial for persistent session
    process.stdin.resume();
  }

  private getPrompt(): string {
    const sessionInfo = chalk.dim(`[${this.sessionCount}]`);
    return `${sessionInfo} `;
  }

  private async handleInput(input: string): Promise<void> {
    if (!input) {
      this.rl.prompt();
      return;
    }

    // Check if we're in chat mode and delegate to chat interface
    if (this.chatInterface.isInChat()) {
      await this.chatInterface.handleChatInput(input.trim());
      return;
    }

    // Handle exit commands (always allowed)
    if (input === 'exit' || input === 'quit' || input === '/exit' || input === '/quit') {
      this.exit();
      return;
    }

    // Check if initialization is required
    if (!this.configManager.isInitialized()) {
      // Only allow init, help, and clear commands when not initialized
      switch (input.toLowerCase()) {
        case 'help':
        case '/help':
          this.showHelp();
          break;
        case 'clear':
        case '/clear':
          this.showClear();
          return; // Don't increment counter for clear
        case 'init':
        case '/init':
          this.showInit();
          break;
        default:
          if (input.toLowerCase().startsWith('/init ') || input.toLowerCase().startsWith('init ')) {
            await this.handleInitCommand(input);
          } else {
            console.log(chalk.yellow('⚠️  Please complete initialization first. Run ') + chalk.cyan('/init') + chalk.yellow(' to get started.'));
          }
          break;
      }
    } else {
      // Normal operation when initialized
      switch (input.toLowerCase()) {
        case 'help':
        case '/help':
          this.showHelp();
          break;
        case 'clear':
        case '/clear':
          this.showClear();
          return; // Don't increment counter for clear
        case 'chat':
        case '/chat':
          await this.showChat();
          return; // Don't increment counter or show prompt when entering chat mode
        case 'configure':
        case '/configure':
          this.showConfigure();
          break;
        case 'generate':
        case '/generate':
          this.showGenerate();
          break;
        case 'playground':
        case '/playground':
          this.showPlayground();
          break;
        case 'init':
        case '/init':
          this.showInit();
          break;
        default:
          if (input.toLowerCase().startsWith('/init ') || input.toLowerCase().startsWith('init ')) {
            await this.handleInitCommand(input);
          } else if (input.toLowerCase().startsWith('/configure ') || input.toLowerCase().startsWith('configure ')) {
            this.handleConfigureCommand(input);
          } else {
            // Echo unknown input
            console.log(chalk.green('Echo:'), input);
          }
          break;
      }
    }

    this.sessionCount++;
    this.rl.setPrompt(this.getPrompt());
    this.rl.prompt();
  }

  private showWelcome(): void {
    UIComponents.showMainInfoBox();
  }

  private showInitializationRequired(): void {
    UIComponents.showInitializationInfoBox();
  }

  private showHelp(): void {
    const helpText = `
${chalk.blue('Available Commands:')}
  /chat                          Interactive AI chat for data needs
  /configure                     Configure CLI settings
  /generate                      Generate synthetic data
  /playground                    Interactive data playground
  /init                          Initialize and setup configuration
  /clear                         Clear the screen and show welcome
  /exit, /quit                   Exit the CLI
`;
    console.log(helpText);
  }

  private showClear(): void {
    console.clear();
    this.showBanner();

    // Check if initialization is required
    if (!this.configManager.isInitialized()) {
      this.showInitializationRequired();
    } else {
      this.showWelcome();
    }

    // Reset session counter and update prompt
    this.sessionCount = 0;
    this.rl.setPrompt(this.getPrompt());
    this.rl.prompt();
  }

  private showBanner(): void {
    UIComponents.showBanner();
  }

  private async showChat(): Promise<void> {
    console.log(chalk.green('🚀 Starting AI Chat Mode...'));
    console.log(chalk.dim('Type /exit-chat to return to the main interface\n'));

    // Start the chat interface
    await this.chatInterface.startChatMode();
  }

  private showInit(): void {
    const initText = `
${chalk.bold.blue('Initialize SynthEngyne CLI')}

${chalk.blue('Authentication Setup:')}
  • ${chalk.cyan('DataGen Platform')} - Connect to your DataGen account
  • ${chalk.cyan('OpenRouter API')} - Use OpenRouter for AI model access
  • ${chalk.cyan('Ollama API')} - Connect to local or remote Ollama instance

${chalk.blue('Setup Commands:')}
  ${chalk.dim('/init datagen')}        Login to DataGen platform
  ${chalk.dim('/init openrouter')}     Configure OpenRouter API key
  ${chalk.dim('/init ollama')}         Setup Ollama API connection

${chalk.yellow('Note:')} You must complete at least one authentication method to use SynthEngyne CLI.
`;
    console.log(initText);
  }

  private showConfigure(): void {
    const configText = `
${chalk.bold.blue('Configure SynthEngyne CLI')}

${chalk.blue('Configuration Options:')}
  • ${chalk.cyan('Output Directory')} - Set default output location
  • ${chalk.cyan('Default Model')} - Choose preferred AI model
  • ${chalk.cyan('Data Format')} - Set default format (JSON, CSV, Parquet)
  • ${chalk.cyan('Generation Parameters')} - Temperature, tokens, etc.

${chalk.blue('Configuration Commands:')}
  ${chalk.dim('/configure show')}      Display current configuration
  ${chalk.dim('/configure output')}    Set output directory
  ${chalk.dim('/configure model')}     Set default model
  ${chalk.dim('/configure format')}    Set default data format
  ${chalk.dim('/configure reset')}     Reset to default settings
  ${chalk.dim('/configure export')}    Export configuration file

${chalk.dim('Configuration management features coming soon...')}
`;
    console.log(configText);
  }

  private showGenerate(): void {
    const generateText = `
${chalk.bold.blue('Generate Synthetic Data')}

${chalk.blue('Available Data Types:')}
  • Text data (articles, reviews, conversations)
  • Tabular data (customers, transactions, products)
  • Time series (metrics, sensor data, financial)
  • Images (synthetic photos, augmented datasets)

${chalk.blue('Example Usage:')}
  ${chalk.dim('generate customer-data --count 1000')}
  ${chalk.dim('generate text-reviews --category electronics')}
  ${chalk.dim('generate time-series --type financial')}

${chalk.dim('Data generation features coming soon...')}
`;
    console.log(generateText);
  }

  private showPlayground(): void {
    const playgroundText = `
${chalk.bold.blue('Interactive Data Playground')}

${chalk.blue('Playground Features:')}
  • Live data preview and editing
  • Interactive schema design
  • Real-time generation testing
  • Data quality validation
  • Export and sharing options

${chalk.blue('Playground Modes:')}
  • Quick Start: Generate sample data instantly
  • Schema Builder: Design custom data structures
  • Template Library: Use pre-built templates
  • Validation Suite: Test data quality

${chalk.dim('Interactive playground coming soon...')}
`;
    console.log(playgroundText);
  }

  private async handleInitCommand(input: string): Promise<void> {
    const parts = input.toLowerCase().split(' ');
    const command = parts[1];

    switch (command) {
      case 'datagen':
        await this.setupDataGen();
        break;
      case 'openrouter':
        await this.setupOpenRouter();
        break;
      case 'ollama':
        await this.setupOllama();
        break;

      default:
        this.showInit();
        break;
    }
  }

  private handleConfigureCommand(input: string): void {
    const parts = input.toLowerCase().split(' ');
    const command = parts[1];

    switch (command) {
      case 'show':
        const config = this.configManager.getConfig();
        console.log(chalk.blue('📋 Current Configuration:'));
        console.log(chalk.dim(JSON.stringify(config, null, 2)));
        break;
      case 'output':
        console.log(chalk.blue('📁 Output Directory Configuration'));
        console.log(chalk.yellow('Feature coming soon - Interactive directory setup'));
        break;
      case 'model':
        console.log(chalk.blue('🤖 Default Model Configuration'));
        console.log(chalk.yellow('Feature coming soon - Model selection'));
        break;
      case 'format':
        console.log(chalk.blue('📄 Data Format Configuration'));
        console.log(chalk.yellow('Feature coming soon - Format selection'));
        break;
      case 'reset':
        this.configManager.resetConfig();
        console.log(chalk.green('✅ Configuration reset to defaults'));
        break;
      case 'export':
        const exportedConfig = this.configManager.exportConfig();
        console.log(chalk.blue('📤 Exported Configuration:'));
        console.log(exportedConfig);
        break;
      default:
        this.showConfigure();
        break;
    }
  }

  private async setupDataGen(): Promise<void> {
    console.log(chalk.blue('🔐 DataGen Platform Setup'));
    console.log(chalk.dim('Please enter your DataGen platform token'));
    console.log(chalk.dim('You can find this in your DataGen account settings'));
    console.log();

    try {
      const token = await this.promptForInput('DataGen Token: ');

      if (!token || token.trim().length === 0) {
        console.log(chalk.red('❌ Token cannot be empty. Please try again.'));
        return;
      }

      // Basic validation for token format
      const trimmedToken = token.trim();
      if (trimmedToken.length < 10) {
        console.log(chalk.yellow('⚠️  Warning: Token seems too short. Please verify it\'s correct.'));
        const confirm = await this.promptForInput('Continue anyway? (y/N): ');
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
          console.log(chalk.dim('Setup cancelled.'));
          return;
        }
      }

      // Save the token
      this.configManager.setDataGenToken(trimmedToken);

      console.log(chalk.green('✅ DataGen token saved successfully!'));
      console.log(chalk.blue('🎉 Initialization complete! You can now use all SynthEngyne CLI features.'));
      console.log(chalk.dim('Run /clear to see the normal interface.'));

    } catch (error) {
      console.log(chalk.red('❌ Error during setup:'), (error as Error).message);
    }
  }

  private async setupOpenRouter(): Promise<void> {
    console.log(chalk.blue('🔑 OpenRouter API Setup'));
    console.log(chalk.dim('Please enter your OpenRouter API key from https://openrouter.ai/keys'));
    console.log(chalk.dim('The key should start with "sk-or-v1-"'));
    console.log();

    try {
      const apiKey = await this.promptForInput('OpenRouter API Key: ');

      if (!apiKey || apiKey.trim().length === 0) {
        console.log(chalk.red('❌ API key cannot be empty. Please try again.'));
        return;
      }

      // Basic validation for OpenRouter API key format
      const trimmedKey = apiKey.trim();
      if (!trimmedKey.startsWith('sk-or-v1-')) {
        console.log(chalk.yellow('⚠️  Warning: API key doesn\'t match expected format (should start with "sk-or-v1-")'));
        const confirm = await this.promptForInput('Continue anyway? (y/N): ');
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
          console.log(chalk.dim('Setup cancelled.'));
          return;
        }
      }

      // Save the API key
      this.configManager.setOpenRouterApiKey(trimmedKey);

      console.log(chalk.green('✅ OpenRouter API key saved successfully!'));
      console.log(chalk.blue('🎉 Initialization complete! You can now use all SynthEngyne CLI features.'));
      console.log(chalk.dim('Run /clear to see the normal interface.'));

    } catch (error) {
      console.log(chalk.red('❌ Error during setup:'), (error as Error).message);
    }
  }

  private async setupOllama(): Promise<void> {
    console.log(chalk.blue('🦙 Ollama API Setup'));
    console.log(chalk.dim('Configure your Ollama endpoint and optional API key'));
    console.log(chalk.dim('Default endpoint: http://localhost:11434'));
    console.log();

    try {
      // Get endpoint URL
      const endpoint = await this.promptForInput('Ollama Endpoint URL (press Enter for default): ');
      const ollamaEndpoint = endpoint.trim() || 'http://localhost:11434';

      // Validate URL format
      try {
        new URL(ollamaEndpoint);
      } catch {
        console.log(chalk.red('❌ Invalid URL format. Please enter a valid URL (e.g., http://localhost:11434)'));
        return;
      }

      // Get optional API key
      const apiKey = await this.promptForInput('Ollama API Key (optional, press Enter to skip): ');
      const ollamaApiKey = apiKey.trim() || 'ollama-default-key';

      // Save the configuration
      this.configManager.setOllamaConfig(ollamaApiKey, ollamaEndpoint);

      console.log(chalk.green('✅ Ollama configuration saved successfully!'));
      console.log(chalk.blue(`📡 Endpoint: ${ollamaEndpoint}`));
      if (apiKey.trim()) {
        console.log(chalk.blue('🔑 API key: [CONFIGURED]'));
      } else {
        console.log(chalk.blue('🔑 API key: [NOT SET]'));
      }
      console.log(chalk.blue('🎉 Initialization complete! You can now use all SynthEngyne CLI features.'));
      console.log(chalk.dim('Run /clear to see the normal interface.'));

    } catch (error) {
      console.log(chalk.red('❌ Error during setup:'), (error as Error).message);
    }
  }

  private promptForInput(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      // Use a simple approach with the existing readline interface
      process.stdout.write(prompt);

      // Set up a one-time listener
      const handleInput = (input: string) => {
        this.rl.removeListener('line', handleInput);
        resolve(input);
      };

      this.rl.on('line', handleInput);
    });
  }

  private exit(): void {
    console.log(chalk.dim('\nGoodbye! 👋'));
    process.exit(0);
  }

  public async start(): Promise<void> {
    // Show banner
    this.showBanner();

    // Check if initialization is required
    if (!this.configManager.isInitialized()) {
      this.showInitializationRequired();
    } else {
      this.showWelcome();
    }

    // Start the interactive session
    this.rl.prompt();

    // Keep the process alive indefinitely
    return new Promise(() => {
      // This promise never resolves, keeping the process alive
      // The process will only exit when user types 'exit' or presses Ctrl+C twice
    });
  }
}
