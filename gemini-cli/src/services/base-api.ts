import { ChatMessage, ChatSession } from '../types';

export interface APIServiceConfig {
  apiKey: string;
  baseUrl: string;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

export interface ChatResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export abstract class BaseAPIService {
  protected config: APIServiceConfig;
  protected serviceName: string;

  constructor(config: APIServiceConfig, serviceName: string) {
    this.config = config;
    this.serviceName = serviceName;
  }

  abstract sendMessage(message: string, session: ChatSession): Promise<ChatResponse>;
  abstract testConnection(): Promise<boolean>;
  abstract listModels(): Promise<string[]>;

  protected buildMessageHistory(session: ChatSession, newMessage: string): ChatMessage[] {
    const messages: ChatMessage[] = [];

    // Add system message if this is the first message
    if (session.messages.length === 0) {
      messages.push({
        role: 'assistant',
        content: `You are <PERSON><PERSON><PERSON><PERSON><PERSON> AI, an intelligent assistant specialized in synthetic data generation and data science. You help users with:

- Synthetic data generation strategies and best practices
- Data modeling and schema design
- Data analysis and insights
- Technical questions about data formats and structures
- Code generation for data processing
- Web research for current information when needed

You have access to web search capabilities to provide up-to-date information. Be helpful, accurate, and technical when appropriate.`,
        timestamp: new Date()
      });
    }

    // Add recent conversation history (last 20 messages to manage context)
    const recentMessages = session.messages.slice(-20);
    messages.push(...recentMessages);

    // Add the new message
    messages.push({
      role: 'user',
      content: newMessage,
      timestamp: new Date()
    });

    return messages;
  }

  protected formatMessagesForAPI(messages: ChatMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role === 'assistant' ? 'assistant' : 'user',
      content: msg.content
    }));
  }

  getServiceName(): string {
    return this.serviceName;
  }

  getConfig(): APIServiceConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<APIServiceConfig>): void {
    this.config = { ...this.config, ...updates };
  }
}
