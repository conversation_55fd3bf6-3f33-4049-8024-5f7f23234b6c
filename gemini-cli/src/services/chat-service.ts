import fs from 'fs';
import path from 'path';
import { ConfigManager } from '../config';
import { ChatSession, ChatMessage } from '../types';
import { loadEnvironmentConfig } from '../utils/env-config';
import { BaseAPIService } from './base-api';
import { OpenRouterAPI } from './openrouter-api';
import { OllamaAPI } from './ollama-api';
import { DataGenAPI } from './datagen-api';
import { WebSearchService } from './web-search';

export class ChatService {
  private configManager: ConfigManager;
  private currentSession: ChatSession;
  private apiService: BaseAPIService | null = null;
  private webSearch: WebSearchService;
  private sessionHistory: Map<string, ChatSession> = new Map();

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.webSearch = new WebSearchService();
    this.currentSession = this.createNewSession();
    this.initializeAPIService();
  }

  private createNewSession(): ChatSession {
    return {
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      messages: [],
      startTime: new Date(),
      lastActivity: new Date()
    };
  }

  private initializeAPIService(): void {
    const config = this.configManager.getConfig();
    const envConfig = loadEnvironmentConfig();

    // Priority: DataGen > OpenRouter > Ollama
    if (config.auth.datagenToken) {
      this.apiService = new DataGenAPI({
        apiKey: config.auth.datagenToken,
        baseUrl: envConfig.datagenBaseUrl,
        model: config.app.defaultModel || 'datagen-gpt-4',
        maxTokens: config.app.generationParameters?.maxTokens || 1000,
        temperature: config.app.generationParameters?.temperature || 0.7,
        timeout: envConfig.apiTimeout
      });
    } else if (config.auth.openrouterApiKey) {
      this.apiService = new OpenRouterAPI({
        apiKey: config.auth.openrouterApiKey,
        baseUrl: envConfig.openrouterBaseUrl,
        model: config.app.defaultModel || 'gpt-3.5-turbo',
        maxTokens: config.app.generationParameters?.maxTokens || 1000,
        temperature: config.app.generationParameters?.temperature || 0.7,
        timeout: envConfig.apiTimeout
      });
    } else if (config.auth.ollamaApiKey && config.auth.ollamaEndpoint) {
      this.apiService = new OllamaAPI({
        apiKey: config.auth.ollamaApiKey,
        baseUrl: config.auth.ollamaEndpoint,
        model: config.app.defaultModel || 'llama2',
        maxTokens: config.app.generationParameters?.maxTokens || 1000,
        temperature: config.app.generationParameters?.temperature || 0.7,
        timeout: envConfig.apiTimeout
      });
    }
  }

  async sendMessage(message: string): Promise<string> {
    if (!this.apiService) {
      throw new Error('No API service configured. Please run /init to set up authentication.');
    }

    // Check if the message contains a web search request
    const searchMatch = message.match(/(?:search|look up|find information about|web search):\s*(.+)/i);
    let enhancedMessage = message;

    if (searchMatch) {
      const searchQuery = searchMatch[1].trim();
      // Use contextual search that includes memory from previous searches
      const searchResults = await this.webSearch.searchWithContext(searchQuery, { maxResults: 3 });
      enhancedMessage = `${message}\n\nWeb search results with context:\n${searchResults}`;
    }

    // Add user message to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    this.currentSession.messages.push(userMessage);
    this.currentSession.lastActivity = new Date();

    try {
      // Send message to API service
      const response = await this.apiService.sendMessage(enhancedMessage, this.currentSession);

      // Add assistant response to session
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.content,
        timestamp: new Date()
      };
      this.currentSession.messages.push(assistantMessage);
      this.currentSession.lastActivity = new Date();

      return response.content;
    } catch (error) {
      const errorMessage = `Error: ${(error as Error).message}`;

      // Add error message to session
      const errorChatMessage: ChatMessage = {
        role: 'assistant',
        content: errorMessage,
        timestamp: new Date()
      };
      this.currentSession.messages.push(errorChatMessage);

      throw error;
    }
  }

  getCurrentSession(): ChatSession {
    return { ...this.currentSession };
  }

  clearCurrentSession(): void {
    this.sessionHistory.set(this.currentSession.id, { ...this.currentSession });
    this.currentSession = this.createNewSession();
  }

  getAPIServiceInfo(): string {
    if (!this.apiService) {
      return 'No API service configured';
    }
    return this.apiService.getServiceName();
  }

  async testConnection(): Promise<boolean> {
    if (!this.apiService) {
      return false;
    }
    return await this.apiService.testConnection();
  }

  async saveSession(filename?: string): Promise<string> {
    const outputDir = this.configManager.getConfig().app.outputDirectory || 'synthengyne-output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const sessionFilename = filename || `chat_session_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    const filepath = path.join(outputDir, sessionFilename);

    const sessionData = {
      session: this.currentSession,
      apiService: this.apiService?.getServiceName(),
      webSearchHistory: this.webSearch.exportSearchHistory(),
      savedAt: new Date().toISOString()
    };

    fs.writeFileSync(filepath, JSON.stringify(sessionData, null, 2));
    return filepath;
  }

  async loadSession(filename: string): Promise<void> {
    const outputDir = this.configManager.getConfig().app.outputDirectory || 'synthengyne-output';
    const filepath = path.join(outputDir, filename);

    if (!fs.existsSync(filepath)) {
      throw new Error(`Session file not found: ${filename}`);
    }

    const sessionData = JSON.parse(fs.readFileSync(filepath, 'utf8'));

    if (sessionData.session) {
      // Save current session to history
      this.sessionHistory.set(this.currentSession.id, { ...this.currentSession });

      // Load the saved session
      this.currentSession = {
        ...sessionData.session,
        lastActivity: new Date()
      };

      // Restore web search history if available
      if (sessionData.webSearchHistory) {
        this.webSearch.importSearchHistory(sessionData.webSearchHistory);
      }
    } else {
      throw new Error('Invalid session file format');
    }
  }

  getSessionHistory(): ChatSession[] {
    return Array.from(this.sessionHistory.values());
  }

  // Web search management methods
  getWebSearchHistory(): Map<string, any[]> {
    return this.webSearch.getSearchHistory();
  }

  getRecentSearches(limit: number = 5): string[] {
    return this.webSearch.getRecentSearches(limit);
  }

  clearWebSearchHistory(): void {
    this.webSearch.clearHistory();
  }

  async performContextualSearch(query: string): Promise<string> {
    return await this.webSearch.searchWithContext(query, { maxResults: 5 });
  }

  // MCP-like functionality for context management
  getConversationContext(): any {
    return {
      currentSession: this.currentSession,
      recentSearches: this.getRecentSearches(10),
      apiService: this.apiService?.getServiceName(),
      sessionCount: this.sessionHistory.size
    };
  }

  async enhanceMessageWithContext(message: string): Promise<string> {
    // Check if message might benefit from web search context
    const searchKeywords = ['latest', 'current', 'recent', 'new', 'update', 'trend', 'what is', 'how to'];
    const needsWebSearch = searchKeywords.some(keyword =>
      message.toLowerCase().includes(keyword)
    );

    if (needsWebSearch && !message.includes('search:')) {
      // Automatically enhance with web search
      const searchQuery = this.extractSearchQuery(message);
      if (searchQuery) {
        const searchResults = await this.webSearch.searchWithContext(searchQuery, { maxResults: 2 });
        return `${message}\n\nAutomatically retrieved context:\n${searchResults}`;
      }
    }

    return message;
  }

  private extractSearchQuery(message: string): string | null {
    // Extract potential search query from message
    const patterns = [
      /what (?:is|are) (.+?)[\?\.]?$/i,
      /how to (.+?)[\?\.]?$/i,
      /latest (.+?)[\?\.]?$/i,
      /current (.+?)[\?\.]?$/i,
      /recent (.+?)[\?\.]?$/i,
      /new (.+?)[\?\.]?$/i,
      /trends? (?:in|for) (.+?)[\?\.]?$/i
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }
}
