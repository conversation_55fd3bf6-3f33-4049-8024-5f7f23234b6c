import fetch from 'node-fetch';
import { BaseAPIService, APIServiceConfig, ChatResponse } from './base-api';
import { ChatSession } from '../types';

export class OpenRouterAPI extends BaseAPIService {
  constructor(config: APIServiceConfig) {
    super(config, 'OpenRouter');
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: this.config.timeout
      });

      return response.ok;
    } catch (error) {
      console.error('OpenRouter connection test failed:', error);
      return false;
    }
  }

  async listModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.config.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: this.config.timeout
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status}`);
      }

      const data = await response.json() as any;
      return data.data?.map((model: any) => model.id) || [];
    } catch (error) {
      console.error('Failed to list OpenRouter models:', error);
      return ['gpt-3.5-turbo', 'gpt-4', 'claude-3-sonnet'];
    }
  }

  async sendMessage(message: string, session: ChatSession): Promise<ChatResponse> {
    const messages = this.buildMessageHistory(session, message);
    const apiMessages = this.formatMessagesForAPI(messages);

    const requestBody = {
      model: this.config.model,
      messages: apiMessages,
      max_tokens: this.config.maxTokens,
      temperature: this.config.temperature,
      stream: false
    };

    try {
      const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://synthengyne.cli',
          'X-Title': 'SynthEngyne CLI'
        },
        body: JSON.stringify(requestBody),
        timeout: this.config.timeout
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}\n${errorData}`);
      }

      const data = await response.json() as any;

      if (!data.choices || data.choices.length === 0) {
        throw new Error('No response generated from OpenRouter API');
      }

      const content = data.choices[0].message.content;
      const usage = data.usage ? {
        promptTokens: data.usage.prompt_tokens || 0,
        completionTokens: data.usage.completion_tokens || 0,
        totalTokens: data.usage.total_tokens || 0
      } : undefined;

      return {
        content,
        usage
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Unexpected error: ${error}`);
    }
  }
}
