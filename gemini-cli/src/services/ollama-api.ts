import fetch from 'node-fetch';
import { BaseAPIService, APIServiceConfig, ChatResponse } from './base-api';
import { ChatSession } from '../types';

export class OllamaAPI extends BaseAPIService {
  constructor(config: APIServiceConfig) {
    super(config, 'Ollama');
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/tags`, {
        method: 'GET',
        timeout: this.config.timeout
      });

      return response.ok;
    } catch (error) {
      console.error('Ollama connection test failed:', error);
      return false;
    }
  }

  async listModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/tags`, {
        method: 'GET',
        timeout: this.config.timeout
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status}`);
      }

      const data = await response.json() as any;
      return data.models?.map((model: any) => model.name) || [];
    } catch (error) {
      console.error('Failed to list Ollama models:', error);
      return ['llama2', 'codellama', 'mistral'];
    }
  }

  async sendMessage(message: string, session: ChatSession): Promise<ChatResponse> {
    const messages = this.buildMessageHistory(session, message);
    
    // Convert to Ollama format - combine all messages into a single prompt
    let prompt = '';
    for (const msg of messages) {
      if (msg.role === 'user') {
        prompt += `Human: ${msg.content}\n\n`;
      } else {
        prompt += `Assistant: ${msg.content}\n\n`;
      }
    }
    prompt += 'Assistant: ';

    const requestBody = {
      model: this.config.model,
      prompt: prompt,
      stream: false,
      options: {
        temperature: this.config.temperature,
        num_predict: this.config.maxTokens
      }
    };

    try {
      const response = await fetch(`${this.config.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        timeout: this.config.timeout
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}\n${errorData}`);
      }

      const data = await response.json() as any;

      if (!data.response) {
        throw new Error('No response generated from Ollama API');
      }

      return {
        content: data.response.trim(),
        usage: {
          promptTokens: data.prompt_eval_count || 0,
          completionTokens: data.eval_count || 0,
          totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
        }
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Unexpected error: ${error}`);
    }
  }
}
