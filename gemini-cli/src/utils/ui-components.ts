import chalk from 'chalk';

/**
 * Centralized UI components for consistent banner display
 * across all screens in the Synthengyne CLI application
 */
export class UIComponents {

  /**
   * Display the consistent SYNTHENGYNE banner using ASCII art
   * This should be used across all screens for consistency
   */
  static showBanner(): void {
    const banner = `
 ███████╗██╗   ██╗███╗   ██╗████████╗██╗  ██╗███████╗███╗   ██╗ ██████╗██╗   ██╗███╗   ██╗███████╗
 ██╔════╝╚██╗ ██╔╝████╗  ██║╚══██╔══╝██║  ██║██╔════╝████╗  ██║██╔════╝╚██╗ ██╔╝████╗  ██║██╔════╝
 ███████╗ ╚████╔╝ ██╔██╗ ██║   ██║   ███████║█████╗  ██╔██╗ ██║██║  ███╗╚████╔╝ ██╔██╗ ██║█████╗
 ╚════██║  ╚██╔╝  ██║╚██╗██║   ██║   ██╔══██║██╔══╝  ██║╚██╗██║██║   ██║ ╚██╔╝  ██║╚██╗██║██╔══╝
 ███████║   ██║   ██║ ╚████║   ██║   ██║  ██║███████╗██║ ╚████║╚██████╔╝  ██║   ██║ ╚████║███████╗
 ╚══════╝   ╚═╝   ╚═╝  ╚═══╝   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝   ╚═╝   ╚═╝  ╚═══╝╚══════╝`;

    console.log(chalk.blue(banner));
    console.log(); // Add spacing after banner
  }

  /**
   * Clear screen and show banner only
   * This is the main method to use for consistent screen transitions
   */
  static showScreen(): void {
    // Clear screen
    console.clear();

    // Show consistent banner
    this.showBanner();
  }

  /**
   * Show just the banner without clearing screen
   * Useful for refreshing the display without losing scroll history
   */
  static refreshDisplay(): void {
    this.showBanner();
  }
}
