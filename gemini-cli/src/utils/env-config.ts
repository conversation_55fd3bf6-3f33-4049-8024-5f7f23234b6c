/**
 * Environment Configuration Utility
 * Centralizes all environment variable handling with type safety and defaults
 */

export interface EnvironmentConfig {
  // API Keys
  openrouterApiKey: string;
  ollamaApiKey: string;
  datagenToken: string;

  // API URLs and Endpoints
  ollamaUrl: string;
  openrouterBaseUrl: string;
  datagenBaseUrl: string;

  // Application Configuration
  defaultModel: string;
  outputDirectory: string;
  dataFormat: 'json' | 'csv' | 'parquet';
  maxTokens: number;
  temperature: number;
  topP: number;

  // Timeouts and Limits
  apiTimeout: number;
  maxSamples: number;
  defaultBatchSize: number;
  compressionLevel: number;

  // Feature Flags
  enableValidation: boolean;
  autoSave: boolean;
  verbose: boolean;

  // Theme and UI
  theme: 'dark' | 'light' | 'auto';
}

/**
 * Get environment variable with fallback to default value
 */
function getEnvVar(key: string, defaultValue: string): string {
  return process.env[key] || defaultValue;
}

/**
 * Get environment variable as number with fallback to default value
 */
function getEnvNumber(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Get environment variable as float with fallback to default value
 */
function getEnvFloat(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (value === undefined) return defaultValue;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Get environment variable as boolean with fallback to default value
 */
function getEnvBoolean(key: string, defaultValue: boolean): boolean {
  const value = process.env[key];
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
}

/**
 * Load and validate environment configuration
 */
export function loadEnvironmentConfig(): EnvironmentConfig {
  return {
    // API Keys
    openrouterApiKey: getEnvVar('OPENROUTER_API_KEY', ''),
    ollamaApiKey: getEnvVar('OLLAMA_API_KEY', ''),
    datagenToken: getEnvVar('DATAGEN_TOKEN', ''),

    // API URLs and Endpoints
    ollamaUrl: getEnvVar('OLLAMA_URL', 'http://localhost:11434'),
    openrouterBaseUrl: getEnvVar('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1'),
    datagenBaseUrl: getEnvVar('DATAGEN_BASE_URL', 'https://api.datagen.tech/v1'),

    // Application Configuration
    defaultModel: getEnvVar('DEFAULT_MODEL', 'gpt-3.5-turbo'),
    outputDirectory: getEnvVar('OUTPUT_DIRECTORY', 'synthengyne-output'),
    dataFormat: getEnvVar('DATA_FORMAT', 'json') as 'json' | 'csv' | 'parquet',
    maxTokens: getEnvNumber('MAX_TOKENS', 1000),
    temperature: getEnvFloat('TEMPERATURE', 0.7),
    topP: getEnvFloat('TOP_P', 1.0),

    // Timeouts and Limits
    apiTimeout: getEnvNumber('API_TIMEOUT', 30000),
    maxSamples: getEnvNumber('MAX_SAMPLES', 1000000),
    defaultBatchSize: getEnvNumber('DEFAULT_BATCH_SIZE', 1000),
    compressionLevel: getEnvNumber('COMPRESSION_LEVEL', 6),

    // Feature Flags
    enableValidation: getEnvBoolean('ENABLE_VALIDATION', true),
    autoSave: getEnvBoolean('AUTO_SAVE', true),
    verbose: getEnvBoolean('VERBOSE', false),

    // Theme and UI
    theme: getEnvVar('THEME', 'auto') as 'dark' | 'light' | 'auto',
  };
}

/**
 * Validate required environment variables
 */
export function validateEnvironmentConfig(config: EnvironmentConfig): string[] {
  const errors: string[] = [];

  // Check for required API keys (at least one should be provided)
  if (!config.datagenToken && !config.openrouterApiKey && !config.ollamaApiKey) {
    errors.push('At least one API key must be provided (DATAGEN_TOKEN, OPENROUTER_API_KEY, or OLLAMA_API_KEY)');
  }

  // Validate URLs
  try {
    new URL(config.ollamaUrl);
  } catch {
    errors.push('OLLAMA_URL must be a valid URL');
  }

  try {
    new URL(config.openrouterBaseUrl);
  } catch {
    errors.push('OPENROUTER_BASE_URL must be a valid URL');
  }

  // Validate data format
  if (!['json', 'csv', 'parquet'].includes(config.dataFormat)) {
    errors.push('DATA_FORMAT must be one of: json, csv, parquet');
  }

  // Validate theme
  if (!['dark', 'light', 'auto'].includes(config.theme)) {
    errors.push('THEME must be one of: dark, light, auto');
  }

  // Validate numeric ranges
  if (config.temperature < 0 || config.temperature > 2) {
    errors.push('TEMPERATURE must be between 0 and 2');
  }

  if (config.topP < 0 || config.topP > 1) {
    errors.push('TOP_P must be between 0 and 1');
  }

  if (config.maxTokens < 1) {
    errors.push('MAX_TOKENS must be greater than 0');
  }

  if (config.apiTimeout < 1000) {
    errors.push('API_TIMEOUT must be at least 1000ms');
  }

  return errors;
}

/**
 * Get environment configuration with validation
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const config = loadEnvironmentConfig();
  const errors = validateEnvironmentConfig(config);

  if (errors.length > 0) {
    console.error('Environment configuration errors:');
    errors.forEach(error => console.error(`  - ${error}`));
    throw new Error('Invalid environment configuration');
  }

  return config;
}
