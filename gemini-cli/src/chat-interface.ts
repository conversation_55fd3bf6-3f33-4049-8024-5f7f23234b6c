import * as readline from 'readline';
import chalk from 'chalk';
import { ConfigManager } from './config';
import { ChatService } from './services/chat-service';
import { UIComponents } from './utils/ui-components';

export class ChatInterface {
  private rl: readline.Interface;
  private configManager: ConfigManager;
  private chatService: ChatService;
  private isInChatMode: boolean = false;
  private headerHeight: number = 0;

  constructor(configManager: ConfigManager, rl?: readline.Interface) {
    this.configManager = configManager;
    this.chatService = new ChatService(configManager);

    if (rl) {
      this.rl = rl;
      // Don't set up event handlers when using shared readline interface
    } else {
      this.rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
        terminal: true,
        historySize: 1000,
        removeHistoryDuplicates: true
      });
      this.setupEventHandlers();
    }
  }

  private setupEventHandlers(): void {
    this.rl.on('line', async (input: string) => {
      if (this.isInChatMode) {
        await this.handleChatInput(input.trim());
      }
    });

    this.rl.on('SIGINT', () => {
      if (this.isInChatMode) {
        console.log(chalk.cyan('\n\nPress Ctrl+C again to exit chat, or type "/exit-chat" to return to main CLI.'));
        this.showChatPrompt();
      }
    });
  }

  private showFixedHeader(): void {
    // Clear screen and move cursor to top
    process.stdout.write('\x1b[2J\x1b[H');

    // Show consistent banner and chat info box
    UIComponents.showScreen('chat');
  }



  private showChatPrompt(): void {
    const session = this.chatService.getCurrentSession();
    const messageCount = session.messages.filter(m => m.role === 'user').length;
    process.stdout.write(chalk.blue(`💬 [${messageCount}] `));
  }

  public async handleChatInput(input: string): Promise<void> {
    if (!input) {
      this.showChatPrompt();
      return;
    }

    // Handle chat commands
    if (input.startsWith('/')) {
      await this.handleChatCommand(input);
      return;
    }

    // Show user message
    console.log(chalk.green(`You: ${input}`));

    // Show typing indicator
    process.stdout.write(chalk.dim('🤖 SynthEngyne AI is thinking...'));

    try {
      const response = await this.chatService.sendMessage(input);

      // Clear typing indicator
      process.stdout.write('\r\x1b[K');

      // Show AI response
      console.log(chalk.blue(`🤖 SynthEngyne AI: ${response}`));
      console.log(); // Add spacing

    } catch (error) {
      // Clear typing indicator
      process.stdout.write('\r\x1b[K');

      console.log(chalk.red(`❌ Error: ${(error as Error).message}`));
      console.log(); // Add spacing
    }

    this.showChatPrompt();
  }

  private async handleChatCommand(command: string): Promise<void> {
    const [cmd, ...args] = command.split(' ');

    switch (cmd.toLowerCase()) {
      case '/save-chat':
        await this.saveChatSession(args[0]);
        break;
      case '/load-chat':
        await this.loadChatSession(args[0]);
        break;
      case '/clear-chat':
        this.clearChatSession();
        break;
      case '/search-history':
        this.showSearchHistory();
        break;
      case '/clear-search':
        this.clearSearchHistory();
        break;
      case '/context':
        this.showConversationContext();
        break;
      case '/exit-chat':
        this.exitChatMode();
        return;
      case '/chat-help':
        this.showChatHelp();
        break;
      default:
        console.log(chalk.yellow(`Unknown command: ${cmd}`));
        console.log(chalk.dim('Type /chat-help for available commands'));
    }

    this.showChatPrompt();
  }

  private async saveChatSession(filename?: string): Promise<void> {
    try {
      const filepath = await this.chatService.saveSession(filename);
      console.log(chalk.green(`✅ Chat session saved to: ${filepath}`));
    } catch (error) {
      console.log(chalk.red(`❌ Failed to save session: ${(error as Error).message}`));
    }
  }

  private async loadChatSession(filename?: string): Promise<void> {
    if (!filename) {
      console.log(chalk.yellow('Please specify a filename: /load-chat <filename>'));
      return;
    }

    try {
      await this.chatService.loadSession(filename);
      console.log(chalk.green(`✅ Chat session loaded from: ${filename}`));

      // Show conversation history
      const session = this.chatService.getCurrentSession();
      console.log(chalk.dim(`Loaded ${session.messages.length} messages from ${session.startTime.toLocaleString()}`));

      // Show last few messages for context
      const recentMessages = session.messages.slice(-4);
      for (const msg of recentMessages) {
        const role = msg.role === 'user' ? 'You' : '🤖 SynthEngyne AI';
        const color = msg.role === 'user' ? chalk.green : chalk.blue;
        console.log(color(`${role}: ${msg.content.substring(0, 100)}${msg.content.length > 100 ? '...' : ''}`));
      }
      console.log();

    } catch (error) {
      console.log(chalk.red(`❌ Failed to load session: ${(error as Error).message}`));
    }
  }

  private clearChatSession(): void {
    this.chatService.clearCurrentSession();

    // Clear screen and show streamlined interface
    process.stdout.write('\x1b[2J\x1b[H');
    UIComponents.showScreen('chat');

    console.log(chalk.green('✅ Chat session cleared. Starting fresh conversation.'));
    console.log();
    this.showChatPrompt();
  }

  private showSearchHistory(): void {
    const recentSearches = this.chatService.getRecentSearches(10);

    if (recentSearches.length === 0) {
      console.log(chalk.yellow('No search history available.'));
      return;
    }

    console.log(chalk.bold.blue('\n📊 Recent Web Searches:'));
    console.log(chalk.dim('─'.repeat(50)));

    recentSearches.forEach((search, index) => {
      const searchQuery = search.split('_')[0];
      console.log(chalk.cyan(`${index + 1}. ${searchQuery}`));
    });

    console.log(chalk.dim('─'.repeat(50)));
    console.log(chalk.dim('Use /clear-search to clear search history\n'));
  }

  private clearSearchHistory(): void {
    this.chatService.clearWebSearchHistory();
    console.log(chalk.green('✅ Web search history cleared.'));
  }

  private showConversationContext(): void {
    const context = this.chatService.getConversationContext();

    console.log(chalk.bold.blue('\n🧠 Conversation Context:'));
    console.log(chalk.dim('─'.repeat(50)));
    console.log(chalk.cyan(`Active Service: ${context.apiService || 'None'}`));
    console.log(chalk.cyan(`Messages in Session: ${context.currentSession.messages.length}`));
    console.log(chalk.cyan(`Session Started: ${context.currentSession.startTime.toLocaleString()}`));
    console.log(chalk.cyan(`Total Sessions: ${context.sessionCount}`));

    if (context.recentSearches.length > 0) {
      console.log(chalk.cyan(`Recent Searches: ${context.recentSearches.length}`));
      context.recentSearches.slice(0, 3).forEach((search: string) => {
        const searchQuery = search.split('_')[0];
        console.log(chalk.dim(`  • ${searchQuery}`));
      });
    }

    console.log(chalk.dim('─'.repeat(50) + '\n'));
  }

  private showChatHelp(): void {
    const helpText = `
${chalk.bold.cyan('🤖 SynthEngyne AI Chat - Command Reference')}

${chalk.bold.blue('Session Management:')}
${chalk.cyan('  /save-chat [filename]')} - Save current conversation to file
${chalk.cyan('  /load-chat <filename>')} - Load a previously saved conversation
${chalk.cyan('  /clear-chat')}          - Clear current conversation and start fresh

${chalk.bold.blue('Search Commands:')}
${chalk.cyan('  /search-history')}      - Show recent web search history
${chalk.cyan('  /clear-search')}        - Clear web search history

${chalk.bold.blue('Utility Commands:')}
${chalk.cyan('  /context')}             - Show conversation context and statistics
${chalk.cyan('  /chat-help')}           - Show this help message
${chalk.cyan('  /exit-chat')}           - Exit chat mode and return to main CLI

${chalk.bold.blue('Chat Features:')}
• ${chalk.green('Natural conversation')} - Ask questions, get help with data generation
• ${chalk.green('Web search with memory')} - Use "search: <query>" to get current information
• ${chalk.green('Contextual search')} - Search results include context from previous searches
• ${chalk.green('Persistent memory')} - AI remembers the entire conversation context
• ${chalk.green('Code generation')} - Ask for code examples and data schemas
• ${chalk.green('MCP integration')} - Model Context Protocol for enhanced web access

${chalk.bold.blue('Example queries:')}
${chalk.dim('  "Help me design a schema for user profiles"')}
${chalk.dim('  "Search: latest trends in synthetic data generation"')}
${chalk.dim('  "Generate Python code to create sample e-commerce data"')}
${chalk.dim('  "What are current best practices for data anonymization?"')}
${chalk.dim('  "Search: new GDPR requirements for synthetic data"')}
`;
    console.log(helpText);
  }

  public async startChatMode(): Promise<void> {
    this.isInChatMode = true;

    // Show banner only once during initial transition
    this.showInitialTransition();

    // Test connection
    const connected = await this.chatService.testConnection();
    if (!connected) {
      console.log(chalk.yellow('⚠️  Warning: Could not connect to AI service. Some features may not work.'));
      console.log();
    }

    console.log(chalk.green('🎉 Welcome to SynthEngyne AI Chat! Type your message or /chat-help for commands.'));
    console.log();

    this.showChatPrompt();
  }

  private showInitialTransition(): void {
    // Clear screen and move cursor to top
    process.stdout.write('\x1b[2J\x1b[H');

    // Show consistent banner and chat info box
    UIComponents.showScreen('chat');
  }

  private exitChatMode(): void {
    this.isInChatMode = false;
    console.log(chalk.dim('\n👋 Exiting chat mode...'));

    // Clear screen and show the main interface
    console.clear();

    // Re-show the banner and welcome screen
    this.showMainInterface();
  }

  private showMainInterface(): void {
    // Show consistent banner and main info box
    UIComponents.showScreen('main');

    // Show prompt
    this.rl.prompt();
  }

  public isInChat(): boolean {
    return this.isInChatMode;
  }

  public getReadlineInterface(): readline.Interface {
    return this.rl;
  }
}
