// SynthEngyne CLI Environment Variables
// Copy this file to .env and fill in your actual values

// API Keys
DATAGEN_TOKEN=your-datagen-token-here
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here
OLLAMA_API_KEY=your-ollama-api-key-here

// API URLs and Endpoints
OLLAMA_URL=http://localhost:11434
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
DATAGEN_BASE_URL=https://api.datagen.tech/v1

// Application Configuration
DEFAULT_MODEL=gpt-3.5-turbo
OUTPUT_DIRECTORY=synthengyne-output
DATA_FORMAT=json
MAX_TOKENS=1000
TEMPERATURE=0.7
TOP_P=1.0

// Timeouts and Limits
API_TIMEOUT=30000
MAX_SAMPLES=1000000
DEFAULT_BATCH_SIZE=1000
COMPRESSION_LEVEL=6

// Feature Flags
ENABLE_VALIDATION=true
AUTO_SAVE=true
VERBOSE=false

// Theme and UI
THEME=auto
